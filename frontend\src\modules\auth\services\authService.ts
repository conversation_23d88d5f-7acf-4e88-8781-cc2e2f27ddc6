import { LoginCredentials, ResetPasswordRequest, AdminResetRequest, AuthResponse } from '../../../types/auth.ts';

// Simulated API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const authService = {
  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    await delay(1500); // Simulate API call

    // Mock validation
    if (!credentials.email || !credentials.password) {
      throw new Error('Email and password are required');
    }

    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return {
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: '1',
            email: credentials.email,
            name: 'Admin User',
            role: 'admin'
          },
          token: 'mock-jwt-token'
        }
      };
    }

    throw new Error('Invalid email or password');
  },

  // Send password reset email
  async requestPasswordReset(request: ResetPasswordRequest): Promise<AuthResponse> {
    await delay(2000); // Simulate API call

    if (!request.email) {
      throw new Error('Email is required');
    }

    // Mock success response
    return {
      success: true,
      message: 'Password reset link sent successfully',
    };
  },

  // Request admin password reset
  async requestAdminReset(request: AdminResetRequest): Promise<AuthResponse> {
    await delay(2000); // Simulate API call

    if (!request.email || !request.employeeId || !request.reason) {
      throw new Error('All fields are required');
    }

    // Mock success response
    return {
      success: true,
      message: 'Admin reset request submitted successfully',
      data: {
        requestId: `PWR-${Date.now().toString().slice(-6)}`
      }
    };
  },

  // Logout user
  async logout(): Promise<void> {
    await delay(500);
    // Clear local storage, cookies, etc.
    localStorage.removeItem('auth-token');
  }
};