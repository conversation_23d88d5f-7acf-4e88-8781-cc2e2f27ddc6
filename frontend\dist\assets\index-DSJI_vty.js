function y1(n,r){for(var o=0;o<r.length;o++){const i=r[o];if(typeof i!="string"&&!Array.isArray(i)){for(const u in i)if(u!=="default"&&!(u in n)){const f=Object.getOwnPropertyDescriptor(i,u);f&&Object.defineProperty(n,u,f.get?f:{enumerable:!0,get:()=>i[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))i(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&i(d)}).observe(document,{childList:!0,subtree:!0});function o(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function i(u){if(u.ep)return;u.ep=!0;const f=o(u);fetch(u.href,f)}})();function Gy(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var uf={exports:{}},vo={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bg;function v1(){if(bg)return vo;bg=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(i,u,f){var d=null;if(f!==void 0&&(d=""+f),u.key!==void 0&&(d=""+u.key),"key"in u){f={};for(var p in u)p!=="key"&&(f[p]=u[p])}else f=u;return u=f.ref,{$$typeof:n,type:i,key:d,ref:u!==void 0?u:null,props:f}}return vo.Fragment=r,vo.jsx=o,vo.jsxs=o,vo}var Sg;function b1(){return Sg||(Sg=1,uf.exports=v1()),uf.exports}var S=b1(),cf={exports:{}},Re={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xg;function S1(){if(xg)return Re;xg=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),x=Symbol.iterator;function R(O){return O===null||typeof O!="object"?null:(O=x&&O[x]||O["@@iterator"],typeof O=="function"?O:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,E={};function j(O,X,le){this.props=O,this.context=X,this.refs=E,this.updater=le||A}j.prototype.isReactComponent={},j.prototype.setState=function(O,X){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,X,"setState")},j.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function N(){}N.prototype=j.prototype;function U(O,X,le){this.props=O,this.context=X,this.refs=E,this.updater=le||A}var B=U.prototype=new N;B.constructor=U,T(B,j.prototype),B.isPureReactComponent=!0;var k=Array.isArray,M={H:null,A:null,T:null,S:null,V:null},_=Object.prototype.hasOwnProperty;function Y(O,X,le,re,ue,ce){return le=ce.ref,{$$typeof:n,type:O,key:X,ref:le!==void 0?le:null,props:ce}}function I(O,X){return Y(O.type,X,void 0,void 0,void 0,O.props)}function F(O){return typeof O=="object"&&O!==null&&O.$$typeof===n}function J(O){var X={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(le){return X[le]})}var te=/\/+/g;function b(O,X){return typeof O=="object"&&O!==null&&O.key!=null?J(""+O.key):X.toString(36)}function Q(){}function P(O){switch(O.status){case"fulfilled":return O.value;case"rejected":throw O.reason;default:switch(typeof O.status=="string"?O.then(Q,Q):(O.status="pending",O.then(function(X){O.status==="pending"&&(O.status="fulfilled",O.value=X)},function(X){O.status==="pending"&&(O.status="rejected",O.reason=X)})),O.status){case"fulfilled":return O.value;case"rejected":throw O.reason}}throw O}function G(O,X,le,re,ue){var ce=typeof O;(ce==="undefined"||ce==="boolean")&&(O=null);var se=!1;if(O===null)se=!0;else switch(ce){case"bigint":case"string":case"number":se=!0;break;case"object":switch(O.$$typeof){case n:case r:se=!0;break;case v:return se=O._init,G(se(O._payload),X,le,re,ue)}}if(se)return ue=ue(O),se=re===""?"."+b(O,0):re,k(ue)?(le="",se!=null&&(le=se.replace(te,"$&/")+"/"),G(ue,X,le,"",function(_e){return _e})):ue!=null&&(F(ue)&&(ue=I(ue,le+(ue.key==null||O&&O.key===ue.key?"":(""+ue.key).replace(te,"$&/")+"/")+se)),X.push(ue)),1;se=0;var Se=re===""?".":re+":";if(k(O))for(var Ce=0;Ce<O.length;Ce++)re=O[Ce],ce=Se+b(re,Ce),se+=G(re,X,le,ce,ue);else if(Ce=R(O),typeof Ce=="function")for(O=Ce.call(O),Ce=0;!(re=O.next()).done;)re=re.value,ce=Se+b(re,Ce++),se+=G(re,X,le,ce,ue);else if(ce==="object"){if(typeof O.then=="function")return G(P(O),X,le,re,ue);throw X=String(O),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return se}function w(O,X,le){if(O==null)return O;var re=[],ue=0;return G(O,re,"","",function(ce){return X.call(le,ce,ue++)}),re}function K(O){if(O._status===-1){var X=O._result;X=X(),X.then(function(le){(O._status===0||O._status===-1)&&(O._status=1,O._result=le)},function(le){(O._status===0||O._status===-1)&&(O._status=2,O._result=le)}),O._status===-1&&(O._status=0,O._result=X)}if(O._status===1)return O._result.default;throw O._result}var oe=typeof reportError=="function"?reportError:function(O){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof O=="object"&&O!==null&&typeof O.message=="string"?String(O.message):String(O),error:O});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",O);return}console.error(O)};function ne(){}return Re.Children={map:w,forEach:function(O,X,le){w(O,function(){X.apply(this,arguments)},le)},count:function(O){var X=0;return w(O,function(){X++}),X},toArray:function(O){return w(O,function(X){return X})||[]},only:function(O){if(!F(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Re.Component=j,Re.Fragment=o,Re.Profiler=u,Re.PureComponent=U,Re.StrictMode=i,Re.Suspense=g,Re.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=M,Re.__COMPILER_RUNTIME={__proto__:null,c:function(O){return M.H.useMemoCache(O)}},Re.cache=function(O){return function(){return O.apply(null,arguments)}},Re.cloneElement=function(O,X,le){if(O==null)throw Error("The argument must be a React element, but you passed "+O+".");var re=T({},O.props),ue=O.key,ce=void 0;if(X!=null)for(se in X.ref!==void 0&&(ce=void 0),X.key!==void 0&&(ue=""+X.key),X)!_.call(X,se)||se==="key"||se==="__self"||se==="__source"||se==="ref"&&X.ref===void 0||(re[se]=X[se]);var se=arguments.length-2;if(se===1)re.children=le;else if(1<se){for(var Se=Array(se),Ce=0;Ce<se;Ce++)Se[Ce]=arguments[Ce+2];re.children=Se}return Y(O.type,ue,void 0,void 0,ce,re)},Re.createContext=function(O){return O={$$typeof:d,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null},O.Provider=O,O.Consumer={$$typeof:f,_context:O},O},Re.createElement=function(O,X,le){var re,ue={},ce=null;if(X!=null)for(re in X.key!==void 0&&(ce=""+X.key),X)_.call(X,re)&&re!=="key"&&re!=="__self"&&re!=="__source"&&(ue[re]=X[re]);var se=arguments.length-2;if(se===1)ue.children=le;else if(1<se){for(var Se=Array(se),Ce=0;Ce<se;Ce++)Se[Ce]=arguments[Ce+2];ue.children=Se}if(O&&O.defaultProps)for(re in se=O.defaultProps,se)ue[re]===void 0&&(ue[re]=se[re]);return Y(O,ce,void 0,void 0,null,ue)},Re.createRef=function(){return{current:null}},Re.forwardRef=function(O){return{$$typeof:p,render:O}},Re.isValidElement=F,Re.lazy=function(O){return{$$typeof:v,_payload:{_status:-1,_result:O},_init:K}},Re.memo=function(O,X){return{$$typeof:h,type:O,compare:X===void 0?null:X}},Re.startTransition=function(O){var X=M.T,le={};M.T=le;try{var re=O(),ue=M.S;ue!==null&&ue(le,re),typeof re=="object"&&re!==null&&typeof re.then=="function"&&re.then(ne,oe)}catch(ce){oe(ce)}finally{M.T=X}},Re.unstable_useCacheRefresh=function(){return M.H.useCacheRefresh()},Re.use=function(O){return M.H.use(O)},Re.useActionState=function(O,X,le){return M.H.useActionState(O,X,le)},Re.useCallback=function(O,X){return M.H.useCallback(O,X)},Re.useContext=function(O){return M.H.useContext(O)},Re.useDebugValue=function(){},Re.useDeferredValue=function(O,X){return M.H.useDeferredValue(O,X)},Re.useEffect=function(O,X,le){var re=M.H;if(typeof le=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return re.useEffect(O,X)},Re.useId=function(){return M.H.useId()},Re.useImperativeHandle=function(O,X,le){return M.H.useImperativeHandle(O,X,le)},Re.useInsertionEffect=function(O,X){return M.H.useInsertionEffect(O,X)},Re.useLayoutEffect=function(O,X){return M.H.useLayoutEffect(O,X)},Re.useMemo=function(O,X){return M.H.useMemo(O,X)},Re.useOptimistic=function(O,X){return M.H.useOptimistic(O,X)},Re.useReducer=function(O,X,le){return M.H.useReducer(O,X,le)},Re.useRef=function(O){return M.H.useRef(O)},Re.useState=function(O){return M.H.useState(O)},Re.useSyncExternalStore=function(O,X,le){return M.H.useSyncExternalStore(O,X,le)},Re.useTransition=function(){return M.H.useTransition()},Re.version="19.1.1",Re}var Cg;function Wf(){return Cg||(Cg=1,cf.exports=S1()),cf.exports}var C=Wf();const Yn=Gy(C),zf=y1({__proto__:null,default:Yn},[C]);var ff={exports:{}},bo={},df={exports:{}},pf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tg;function x1(){return Tg||(Tg=1,(function(n){function r(w,K){var oe=w.length;w.push(K);e:for(;0<oe;){var ne=oe-1>>>1,O=w[ne];if(0<u(O,K))w[ne]=K,w[oe]=O,oe=ne;else break e}}function o(w){return w.length===0?null:w[0]}function i(w){if(w.length===0)return null;var K=w[0],oe=w.pop();if(oe!==K){w[0]=oe;e:for(var ne=0,O=w.length,X=O>>>1;ne<X;){var le=2*(ne+1)-1,re=w[le],ue=le+1,ce=w[ue];if(0>u(re,oe))ue<O&&0>u(ce,re)?(w[ne]=ce,w[ue]=oe,ne=ue):(w[ne]=re,w[le]=oe,ne=le);else if(ue<O&&0>u(ce,oe))w[ne]=ce,w[ue]=oe,ne=ue;else break e}}return K}function u(w,K){var oe=w.sortIndex-K.sortIndex;return oe!==0?oe:w.id-K.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var g=[],h=[],v=1,x=null,R=3,A=!1,T=!1,E=!1,j=!1,N=typeof setTimeout=="function"?setTimeout:null,U=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;function k(w){for(var K=o(h);K!==null;){if(K.callback===null)i(h);else if(K.startTime<=w)i(h),K.sortIndex=K.expirationTime,r(g,K);else break;K=o(h)}}function M(w){if(E=!1,k(w),!T)if(o(g)!==null)T=!0,_||(_=!0,b());else{var K=o(h);K!==null&&G(M,K.startTime-w)}}var _=!1,Y=-1,I=5,F=-1;function J(){return j?!0:!(n.unstable_now()-F<I)}function te(){if(j=!1,_){var w=n.unstable_now();F=w;var K=!0;try{e:{T=!1,E&&(E=!1,U(Y),Y=-1),A=!0;var oe=R;try{t:{for(k(w),x=o(g);x!==null&&!(x.expirationTime>w&&J());){var ne=x.callback;if(typeof ne=="function"){x.callback=null,R=x.priorityLevel;var O=ne(x.expirationTime<=w);if(w=n.unstable_now(),typeof O=="function"){x.callback=O,k(w),K=!0;break t}x===o(g)&&i(g),k(w)}else i(g);x=o(g)}if(x!==null)K=!0;else{var X=o(h);X!==null&&G(M,X.startTime-w),K=!1}}break e}finally{x=null,R=oe,A=!1}K=void 0}}finally{K?b():_=!1}}}var b;if(typeof B=="function")b=function(){B(te)};else if(typeof MessageChannel<"u"){var Q=new MessageChannel,P=Q.port2;Q.port1.onmessage=te,b=function(){P.postMessage(null)}}else b=function(){N(te,0)};function G(w,K){Y=N(function(){w(n.unstable_now())},K)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(w){w.callback=null},n.unstable_forceFrameRate=function(w){0>w||125<w?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<w?Math.floor(1e3/w):5},n.unstable_getCurrentPriorityLevel=function(){return R},n.unstable_next=function(w){switch(R){case 1:case 2:case 3:var K=3;break;default:K=R}var oe=R;R=K;try{return w()}finally{R=oe}},n.unstable_requestPaint=function(){j=!0},n.unstable_runWithPriority=function(w,K){switch(w){case 1:case 2:case 3:case 4:case 5:break;default:w=3}var oe=R;R=w;try{return K()}finally{R=oe}},n.unstable_scheduleCallback=function(w,K,oe){var ne=n.unstable_now();switch(typeof oe=="object"&&oe!==null?(oe=oe.delay,oe=typeof oe=="number"&&0<oe?ne+oe:ne):oe=ne,w){case 1:var O=-1;break;case 2:O=250;break;case 5:O=1073741823;break;case 4:O=1e4;break;default:O=5e3}return O=oe+O,w={id:v++,callback:K,priorityLevel:w,startTime:oe,expirationTime:O,sortIndex:-1},oe>ne?(w.sortIndex=oe,r(h,w),o(g)===null&&w===o(h)&&(E?(U(Y),Y=-1):E=!0,G(M,oe-ne))):(w.sortIndex=O,r(g,w),T||A||(T=!0,_||(_=!0,b()))),w},n.unstable_shouldYield=J,n.unstable_wrapCallback=function(w){var K=R;return function(){var oe=R;R=K;try{return w.apply(this,arguments)}finally{R=oe}}}})(pf)),pf}var Eg;function C1(){return Eg||(Eg=1,df.exports=x1()),df.exports}var mf={exports:{}},Xt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rg;function T1(){if(Rg)return Xt;Rg=1;var n=Wf();function r(g){var h="https://react.dev/errors/"+g;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)h+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+g+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(r(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},u=Symbol.for("react.portal");function f(g,h,v){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:x==null?null:""+x,children:g,containerInfo:h,implementation:v}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(g,h){if(g==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return Xt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,Xt.createPortal=function(g,h){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return f(g,h,null,v)},Xt.flushSync=function(g){var h=d.T,v=i.p;try{if(d.T=null,i.p=2,g)return g()}finally{d.T=h,i.p=v,i.d.f()}},Xt.preconnect=function(g,h){typeof g=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,i.d.C(g,h))},Xt.prefetchDNS=function(g){typeof g=="string"&&i.d.D(g)},Xt.preinit=function(g,h){if(typeof g=="string"&&h&&typeof h.as=="string"){var v=h.as,x=p(v,h.crossOrigin),R=typeof h.integrity=="string"?h.integrity:void 0,A=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;v==="style"?i.d.S(g,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:x,integrity:R,fetchPriority:A}):v==="script"&&i.d.X(g,{crossOrigin:x,integrity:R,fetchPriority:A,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},Xt.preinitModule=function(g,h){if(typeof g=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var v=p(h.as,h.crossOrigin);i.d.M(g,{crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&i.d.M(g)},Xt.preload=function(g,h){if(typeof g=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var v=h.as,x=p(v,h.crossOrigin);i.d.L(g,v,{crossOrigin:x,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},Xt.preloadModule=function(g,h){if(typeof g=="string")if(h){var v=p(h.as,h.crossOrigin);i.d.m(g,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:v,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else i.d.m(g)},Xt.requestFormReset=function(g){i.d.r(g)},Xt.unstable_batchedUpdates=function(g,h){return g(h)},Xt.useFormState=function(g,h,v){return d.H.useFormState(g,h,v)},Xt.useFormStatus=function(){return d.H.useHostTransitionStatus()},Xt.version="19.1.1",Xt}var Ag;function Yy(){if(Ag)return mf.exports;Ag=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),mf.exports=T1(),mf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mg;function E1(){if(Mg)return bo;Mg=1;var n=C1(),r=Wf(),o=Yy();function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(f(e)!==e)throw Error(i(188))}function g(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(i(188));return t!==e?null:e}for(var a=e,l=t;;){var s=a.return;if(s===null)break;var c=s.alternate;if(c===null){if(l=s.return,l!==null){a=l;continue}break}if(s.child===c.child){for(c=s.child;c;){if(c===a)return p(s),e;if(c===l)return p(s),t;c=c.sibling}throw Error(i(188))}if(a.return!==l.return)a=s,l=c;else{for(var m=!1,y=s.child;y;){if(y===a){m=!0,a=s,l=c;break}if(y===l){m=!0,l=s,a=c;break}y=y.sibling}if(!m){for(y=c.child;y;){if(y===a){m=!0,a=c,l=s;break}if(y===l){m=!0,l=c,a=s;break}y=y.sibling}if(!m)throw Error(i(189))}}if(a.alternate!==l)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?e:t}function h(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=h(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,x=Symbol.for("react.element"),R=Symbol.for("react.transitional.element"),A=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),U=Symbol.for("react.consumer"),B=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),Y=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),F=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),te=Symbol.iterator;function b(e){return e===null||typeof e!="object"?null:(e=te&&e[te]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Symbol.for("react.client.reference");function P(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Q?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case T:return"Fragment";case j:return"Profiler";case E:return"StrictMode";case M:return"Suspense";case _:return"SuspenseList";case F:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case A:return"Portal";case B:return(e.displayName||"Context")+".Provider";case U:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Y:return t=e.displayName||null,t!==null?t:P(e.type)||"Memo";case I:t=e._payload,e=e._init;try{return P(e(t))}catch{}}return null}var G=Array.isArray,w=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,oe={pending:!1,data:null,method:null,action:null},ne=[],O=-1;function X(e){return{current:e}}function le(e){0>O||(e.current=ne[O],ne[O]=null,O--)}function re(e,t){O++,ne[O]=e.current,e.current=t}var ue=X(null),ce=X(null),se=X(null),Se=X(null);function Ce(e,t){switch(re(se,t),re(ce,e),re(ue,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Xh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Xh(t),e=Kh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}le(ue),re(ue,e)}function _e(){le(ue),le(ce),le(se)}function xe(e){e.memoizedState!==null&&re(Se,e);var t=ue.current,a=Kh(t,e.type);t!==a&&(re(ce,e),re(ue,a))}function Me(e){ce.current===e&&(le(ue),le(ce)),Se.current===e&&(le(Se),po._currentValue=oe)}var $e=Object.prototype.hasOwnProperty,St=n.unstable_scheduleCallback,Te=n.unstable_cancelCallback,Ke=n.unstable_shouldYield,Ht=n.unstable_requestPaint,Qe=n.unstable_now,pt=n.unstable_getCurrentPriorityLevel,dt=n.unstable_ImmediatePriority,yt=n.unstable_UserBlockingPriority,tt=n.unstable_NormalPriority,pe=n.unstable_LowPriority,rn=n.unstable_IdlePriority,xt=n.log,On=n.unstable_setDisableYieldValue,mt=null,Ee=null;function nt(e){if(typeof xt=="function"&&On(e),Ee&&typeof Ee.setStrictMode=="function")try{Ee.setStrictMode(mt,e)}catch{}}var qe=Math.clz32?Math.clz32:va,Qt=Math.log,at=Math.LN2;function va(e){return e>>>=0,e===0?32:31-(Qt(e)/at|0)|0}var Qn=256,Ka=4194304;function he(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ie(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var s=0,c=e.suspendedLanes,m=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~c,l!==0?s=he(l):(m&=y,m!==0?s=he(m):a||(a=y&~e,a!==0&&(s=he(a))))):(y=l&~c,y!==0?s=he(y):m!==0?s=he(m):a||(a=l&~e,a!==0&&(s=he(a)))),s===0?0:t!==0&&t!==s&&(t&c)===0&&(c=s&-s,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:s}function vt(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function ba(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xl(){var e=Qn;return Qn<<=1,(Qn&4194048)===0&&(Qn=256),e}function kd(){var e=Ka;return Ka<<=1,(Ka&62914560)===0&&(Ka=4194304),e}function Zs(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Cl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function lv(e,t,a,l,s,c){var m=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var y=e.entanglements,z=e.expirationTimes,H=e.hiddenUpdates;for(a=m&~a;0<a;){var W=31-qe(a),ae=1<<W;y[W]=0,z[W]=-1;var q=H[W];if(q!==null)for(H[W]=null,W=0;W<q.length;W++){var V=q[W];V!==null&&(V.lane&=-536870913)}a&=~ae}l!==0&&jd(e,l,0),c!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=c&~(m&~t))}function jd(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-qe(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Bd(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-qe(a),s=1<<l;s&t|e[l]&t&&(e[l]|=t),a&=~s}}function Ws(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Dd(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:pg(e.type))}function ov(e,t){var a=K.p;try{return K.p=e,t()}finally{K.p=a}}var Sa=Math.random().toString(36).slice(2),Yt="__reactFiber$"+Sa,Wt="__reactProps$"+Sa,Tr="__reactContainer$"+Sa,eu="__reactEvents$"+Sa,iv="__reactListeners$"+Sa,sv="__reactHandles$"+Sa,Nd="__reactResources$"+Sa,Tl="__reactMarker$"+Sa;function tu(e){delete e[Yt],delete e[Wt],delete e[eu],delete e[iv],delete e[sv]}function Er(e){var t=e[Yt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Tr]||a[Yt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Wh(e);e!==null;){if(a=e[Yt])return a;e=Wh(e)}return t}e=a,a=e.parentNode}return null}function Rr(e){if(e=e[Yt]||e[Tr]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function El(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function Ar(e){var t=e[Nd];return t||(t=e[Nd]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Nt(e){e[Tl]=!0}var _d=new Set,$d={};function Qa(e,t){Mr(e,t),Mr(e+"Capture",t)}function Mr(e,t){for($d[e]=t,e=0;e<t.length;e++)_d.add(t[e])}var uv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ld={},Ud={};function cv(e){return $e.call(Ud,e)?!0:$e.call(Ld,e)?!1:uv.test(e)?Ud[e]=!0:(Ld[e]=!0,!1)}function Fo(e,t,a){if(cv(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Zo(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Fn(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var nu,Hd;function Or(e){if(nu===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);nu=t&&t[1]||"",Hd=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+nu+e+Hd}var au=!1;function ru(e,t){if(!e||au)return"";au=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var ae=function(){throw Error()};if(Object.defineProperty(ae.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(ae,[])}catch(V){var q=V}Reflect.construct(e,[],ae)}else{try{ae.call()}catch(V){q=V}e.call(ae.prototype)}}else{try{throw Error()}catch(V){q=V}(ae=e())&&typeof ae.catch=="function"&&ae.catch(function(){})}}catch(V){if(V&&q&&typeof V.stack=="string")return[V.stack,q.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),m=c[0],y=c[1];if(m&&y){var z=m.split(`
`),H=y.split(`
`);for(s=l=0;l<z.length&&!z[l].includes("DetermineComponentFrameRoot");)l++;for(;s<H.length&&!H[s].includes("DetermineComponentFrameRoot");)s++;if(l===z.length||s===H.length)for(l=z.length-1,s=H.length-1;1<=l&&0<=s&&z[l]!==H[s];)s--;for(;1<=l&&0<=s;l--,s--)if(z[l]!==H[s]){if(l!==1||s!==1)do if(l--,s--,0>s||z[l]!==H[s]){var W=`
`+z[l].replace(" at new "," at ");return e.displayName&&W.includes("<anonymous>")&&(W=W.replace("<anonymous>",e.displayName)),W}while(1<=l&&0<=s);break}}}finally{au=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Or(a):""}function fv(e){switch(e.tag){case 26:case 27:case 5:return Or(e.type);case 16:return Or("Lazy");case 13:return Or("Suspense");case 19:return Or("SuspenseList");case 0:case 15:return ru(e.type,!1);case 11:return ru(e.type.render,!1);case 1:return ru(e.type,!0);case 31:return Or("Activity");default:return""}}function qd(e){try{var t="";do t+=fv(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function gn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function dv(e){var t=Pd(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(m){l=""+m,c.call(this,m)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(m){l=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wo(e){e._valueTracker||(e._valueTracker=dv(e))}function Vd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Pd(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Jo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var pv=/[\n"\\]/g;function yn(e){return e.replace(pv,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function lu(e,t,a,l,s,c,m,y){e.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.type=m:e.removeAttribute("type"),t!=null?m==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+gn(t)):e.value!==""+gn(t)&&(e.value=""+gn(t)):m!=="submit"&&m!=="reset"||e.removeAttribute("value"),t!=null?ou(e,m,gn(t)):a!=null?ou(e,m,gn(a)):l!=null&&e.removeAttribute("value"),s==null&&c!=null&&(e.defaultChecked=!!c),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+gn(y):e.removeAttribute("name")}function Gd(e,t,a,l,s,c,m,y){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+gn(a):"",t=t!=null?""+gn(t):a,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??s,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(e.name=m)}function ou(e,t,a){t==="number"&&Jo(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function wr(e,t,a,l){if(e=e.options,t){t={};for(var s=0;s<a.length;s++)t["$"+a[s]]=!0;for(a=0;a<e.length;a++)s=t.hasOwnProperty("$"+e[a].value),e[a].selected!==s&&(e[a].selected=s),s&&l&&(e[a].defaultSelected=!0)}else{for(a=""+gn(a),t=null,s=0;s<e.length;s++){if(e[s].value===a){e[s].selected=!0,l&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Yd(e,t,a){if(t!=null&&(t=""+gn(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+gn(a):""}function Id(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(i(92));if(G(l)){if(1<l.length)throw Error(i(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=gn(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function zr(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var mv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Xd(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||mv.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Kd(e,t,a){if(t!=null&&typeof t!="object")throw Error(i(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var s in t)l=t[s],t.hasOwnProperty(s)&&a[s]!==l&&Xd(e,s,l)}else for(var c in t)t.hasOwnProperty(c)&&Xd(e,c,t[c])}function iu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),gv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ei(e){return gv.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var su=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var kr=null,jr=null;function Qd(e){var t=Rr(e);if(t&&(e=t.stateNode)){var a=e[Wt]||null;e:switch(e=t.stateNode,t.type){case"input":if(lu(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+yn(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var s=l[Wt]||null;if(!s)throw Error(i(90));lu(l,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Vd(l)}break e;case"textarea":Yd(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&wr(e,!!a.multiple,t,!1)}}}var cu=!1;function Fd(e,t,a){if(cu)return e(t,a);cu=!0;try{var l=e(t);return l}finally{if(cu=!1,(kr!==null||jr!==null)&&(Ui(),kr&&(t=kr,e=jr,jr=kr=null,Qd(t),e)))for(t=0;t<e.length;t++)Qd(e[t])}}function Rl(e,t){var a=e.stateNode;if(a===null)return null;var l=a[Wt]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(i(231,t,typeof a));return a}var Zn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fu=!1;if(Zn)try{var Al={};Object.defineProperty(Al,"passive",{get:function(){fu=!0}}),window.addEventListener("test",Al,Al),window.removeEventListener("test",Al,Al)}catch{fu=!1}var xa=null,du=null,ti=null;function Zd(){if(ti)return ti;var e,t=du,a=t.length,l,s="value"in xa?xa.value:xa.textContent,c=s.length;for(e=0;e<a&&t[e]===s[e];e++);var m=a-e;for(l=1;l<=m&&t[a-l]===s[c-l];l++);return ti=s.slice(e,1<l?1-l:void 0)}function ni(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ai(){return!0}function Wd(){return!1}function Jt(e){function t(a,l,s,c,m){this._reactName=a,this._targetInst=s,this.type=l,this.nativeEvent=c,this.target=m,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(a=e[y],this[y]=a?a(c):c[y]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ai:Wd,this.isPropagationStopped=Wd,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=ai)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=ai)},persist:function(){},isPersistent:ai}),t}var Fa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ri=Jt(Fa),Ml=v({},Fa,{view:0,detail:0}),yv=Jt(Ml),pu,mu,Ol,li=v({},Ml,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ol&&(Ol&&e.type==="mousemove"?(pu=e.screenX-Ol.screenX,mu=e.screenY-Ol.screenY):mu=pu=0,Ol=e),pu)},movementY:function(e){return"movementY"in e?e.movementY:mu}}),Jd=Jt(li),vv=v({},li,{dataTransfer:0}),bv=Jt(vv),Sv=v({},Ml,{relatedTarget:0}),hu=Jt(Sv),xv=v({},Fa,{animationName:0,elapsedTime:0,pseudoElement:0}),Cv=Jt(xv),Tv=v({},Fa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ev=Jt(Tv),Rv=v({},Fa,{data:0}),ep=Jt(Rv),Av={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Mv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ov={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ov[e])?!!t[e]:!1}function gu(){return wv}var zv=v({},Ml,{key:function(e){if(e.key){var t=Av[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ni(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Mv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gu,charCode:function(e){return e.type==="keypress"?ni(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ni(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),kv=Jt(zv),jv=v({},li,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),tp=Jt(jv),Bv=v({},Ml,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gu}),Dv=Jt(Bv),Nv=v({},Fa,{propertyName:0,elapsedTime:0,pseudoElement:0}),_v=Jt(Nv),$v=v({},li,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Lv=Jt($v),Uv=v({},Fa,{newState:0,oldState:0}),Hv=Jt(Uv),qv=[9,13,27,32],yu=Zn&&"CompositionEvent"in window,wl=null;Zn&&"documentMode"in document&&(wl=document.documentMode);var Pv=Zn&&"TextEvent"in window&&!wl,np=Zn&&(!yu||wl&&8<wl&&11>=wl),ap=" ",rp=!1;function lp(e,t){switch(e){case"keyup":return qv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function op(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Br=!1;function Vv(e,t){switch(e){case"compositionend":return op(t);case"keypress":return t.which!==32?null:(rp=!0,ap);case"textInput":return e=t.data,e===ap&&rp?null:e;default:return null}}function Gv(e,t){if(Br)return e==="compositionend"||!yu&&lp(e,t)?(e=Zd(),ti=du=xa=null,Br=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return np&&t.locale!=="ko"?null:t.data;default:return null}}var Yv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ip(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Yv[e.type]:t==="textarea"}function sp(e,t,a,l){kr?jr?jr.push(l):jr=[l]:kr=l,t=Yi(t,"onChange"),0<t.length&&(a=new ri("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var zl=null,kl=null;function Iv(e){Ph(e,0)}function oi(e){var t=El(e);if(Vd(t))return e}function up(e,t){if(e==="change")return t}var cp=!1;if(Zn){var vu;if(Zn){var bu="oninput"in document;if(!bu){var fp=document.createElement("div");fp.setAttribute("oninput","return;"),bu=typeof fp.oninput=="function"}vu=bu}else vu=!1;cp=vu&&(!document.documentMode||9<document.documentMode)}function dp(){zl&&(zl.detachEvent("onpropertychange",pp),kl=zl=null)}function pp(e){if(e.propertyName==="value"&&oi(kl)){var t=[];sp(t,kl,e,uu(e)),Fd(Iv,t)}}function Xv(e,t,a){e==="focusin"?(dp(),zl=t,kl=a,zl.attachEvent("onpropertychange",pp)):e==="focusout"&&dp()}function Kv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return oi(kl)}function Qv(e,t){if(e==="click")return oi(t)}function Fv(e,t){if(e==="input"||e==="change")return oi(t)}function Zv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ln=typeof Object.is=="function"?Object.is:Zv;function jl(e,t){if(ln(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var s=a[l];if(!$e.call(t,s)||!ln(e[s],t[s]))return!1}return!0}function mp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function hp(e,t){var a=mp(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=mp(a)}}function gp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?gp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yp(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Jo(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Jo(e.document)}return t}function Su(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Wv=Zn&&"documentMode"in document&&11>=document.documentMode,Dr=null,xu=null,Bl=null,Cu=!1;function vp(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Cu||Dr==null||Dr!==Jo(l)||(l=Dr,"selectionStart"in l&&Su(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Bl&&jl(Bl,l)||(Bl=l,l=Yi(xu,"onSelect"),0<l.length&&(t=new ri("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=Dr)))}function Za(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Nr={animationend:Za("Animation","AnimationEnd"),animationiteration:Za("Animation","AnimationIteration"),animationstart:Za("Animation","AnimationStart"),transitionrun:Za("Transition","TransitionRun"),transitionstart:Za("Transition","TransitionStart"),transitioncancel:Za("Transition","TransitionCancel"),transitionend:Za("Transition","TransitionEnd")},Tu={},bp={};Zn&&(bp=document.createElement("div").style,"AnimationEvent"in window||(delete Nr.animationend.animation,delete Nr.animationiteration.animation,delete Nr.animationstart.animation),"TransitionEvent"in window||delete Nr.transitionend.transition);function Wa(e){if(Tu[e])return Tu[e];if(!Nr[e])return e;var t=Nr[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in bp)return Tu[e]=t[a];return e}var Sp=Wa("animationend"),xp=Wa("animationiteration"),Cp=Wa("animationstart"),Jv=Wa("transitionrun"),eb=Wa("transitionstart"),tb=Wa("transitioncancel"),Tp=Wa("transitionend"),Ep=new Map,Eu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Eu.push("scrollEnd");function wn(e,t){Ep.set(e,t),Qa(t,[e])}var Rp=new WeakMap;function vn(e,t){if(typeof e=="object"&&e!==null){var a=Rp.get(e);return a!==void 0?a:(t={value:e,source:t,stack:qd(t)},Rp.set(e,t),t)}return{value:e,source:t,stack:qd(t)}}var bn=[],_r=0,Ru=0;function ii(){for(var e=_r,t=Ru=_r=0;t<e;){var a=bn[t];bn[t++]=null;var l=bn[t];bn[t++]=null;var s=bn[t];bn[t++]=null;var c=bn[t];if(bn[t++]=null,l!==null&&s!==null){var m=l.pending;m===null?s.next=s:(s.next=m.next,m.next=s),l.pending=s}c!==0&&Ap(a,s,c)}}function si(e,t,a,l){bn[_r++]=e,bn[_r++]=t,bn[_r++]=a,bn[_r++]=l,Ru|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Au(e,t,a,l){return si(e,t,a,l),ui(e)}function $r(e,t){return si(e,null,null,t),ui(e)}function Ap(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var s=!1,c=e.return;c!==null;)c.childLanes|=a,l=c.alternate,l!==null&&(l.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(s=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,s&&t!==null&&(s=31-qe(a),e=c.hiddenUpdates,l=e[s],l===null?e[s]=[t]:l.push(t),t.lane=a|536870912),c):null}function ui(e){if(50<ro)throw ro=0,jc=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Lr={};function nb(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function on(e,t,a,l){return new nb(e,t,a,l)}function Mu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wn(e,t){var a=e.alternate;return a===null?(a=on(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Mp(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ci(e,t,a,l,s,c){var m=0;if(l=e,typeof e=="function")Mu(e)&&(m=1);else if(typeof e=="string")m=r1(e,a,ue.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case F:return e=on(31,a,t,s),e.elementType=F,e.lanes=c,e;case T:return Ja(a.children,s,c,t);case E:m=8,s|=24;break;case j:return e=on(12,a,t,s|2),e.elementType=j,e.lanes=c,e;case M:return e=on(13,a,t,s),e.elementType=M,e.lanes=c,e;case _:return e=on(19,a,t,s),e.elementType=_,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case N:case B:m=10;break e;case U:m=9;break e;case k:m=11;break e;case Y:m=14;break e;case I:m=16,l=null;break e}m=29,a=Error(i(130,e===null?"null":typeof e,"")),l=null}return t=on(m,a,t,s),t.elementType=e,t.type=l,t.lanes=c,t}function Ja(e,t,a,l){return e=on(7,e,l,t),e.lanes=a,e}function Ou(e,t,a){return e=on(6,e,null,t),e.lanes=a,e}function wu(e,t,a){return t=on(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ur=[],Hr=0,fi=null,di=0,Sn=[],xn=0,er=null,Jn=1,ea="";function tr(e,t){Ur[Hr++]=di,Ur[Hr++]=fi,fi=e,di=t}function Op(e,t,a){Sn[xn++]=Jn,Sn[xn++]=ea,Sn[xn++]=er,er=e;var l=Jn;e=ea;var s=32-qe(l)-1;l&=~(1<<s),a+=1;var c=32-qe(t)+s;if(30<c){var m=s-s%5;c=(l&(1<<m)-1).toString(32),l>>=m,s-=m,Jn=1<<32-qe(t)+s|a<<s|l,ea=c+e}else Jn=1<<c|a<<s|l,ea=e}function zu(e){e.return!==null&&(tr(e,1),Op(e,1,0))}function ku(e){for(;e===fi;)fi=Ur[--Hr],Ur[Hr]=null,di=Ur[--Hr],Ur[Hr]=null;for(;e===er;)er=Sn[--xn],Sn[xn]=null,ea=Sn[--xn],Sn[xn]=null,Jn=Sn[--xn],Sn[xn]=null}var Ft=null,Ct=null,Pe=!1,nr=null,Dn=!1,ju=Error(i(519));function ar(e){var t=Error(i(418,""));throw _l(vn(t,e)),ju}function wp(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Yt]=e,t[Wt]=l,a){case"dialog":je("cancel",t),je("close",t);break;case"iframe":case"object":case"embed":je("load",t);break;case"video":case"audio":for(a=0;a<oo.length;a++)je(oo[a],t);break;case"source":je("error",t);break;case"img":case"image":case"link":je("error",t),je("load",t);break;case"details":je("toggle",t);break;case"input":je("invalid",t),Gd(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Wo(t);break;case"select":je("invalid",t);break;case"textarea":je("invalid",t),Id(t,l.value,l.defaultValue,l.children),Wo(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||Ih(t.textContent,a)?(l.popover!=null&&(je("beforetoggle",t),je("toggle",t)),l.onScroll!=null&&je("scroll",t),l.onScrollEnd!=null&&je("scrollend",t),l.onClick!=null&&(t.onclick=Ii),t=!0):t=!1,t||ar(e)}function zp(e){for(Ft=e.return;Ft;)switch(Ft.tag){case 5:case 13:Dn=!1;return;case 27:case 3:Dn=!0;return;default:Ft=Ft.return}}function Dl(e){if(e!==Ft)return!1;if(!Pe)return zp(e),Pe=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Kc(e.type,e.memoizedProps)),a=!a),a&&Ct&&ar(e),zp(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Ct=kn(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Ct=null}}else t===27?(t=Ct,$a(e.type)?(e=Wc,Wc=null,Ct=e):Ct=t):Ct=Ft?kn(e.stateNode.nextSibling):null;return!0}function Nl(){Ct=Ft=null,Pe=!1}function kp(){var e=nr;return e!==null&&(nn===null?nn=e:nn.push.apply(nn,e),nr=null),e}function _l(e){nr===null?nr=[e]:nr.push(e)}var Bu=X(null),rr=null,ta=null;function Ca(e,t,a){re(Bu,t._currentValue),t._currentValue=a}function na(e){e._currentValue=Bu.current,le(Bu)}function Du(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Nu(e,t,a,l){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var c=s.dependencies;if(c!==null){var m=s.child;c=c.firstContext;e:for(;c!==null;){var y=c;c=s;for(var z=0;z<t.length;z++)if(y.context===t[z]){c.lanes|=a,y=c.alternate,y!==null&&(y.lanes|=a),Du(c.return,a,e),l||(m=null);break e}c=y.next}}else if(s.tag===18){if(m=s.return,m===null)throw Error(i(341));m.lanes|=a,c=m.alternate,c!==null&&(c.lanes|=a),Du(m,a,e),m=null}else m=s.child;if(m!==null)m.return=s;else for(m=s;m!==null;){if(m===e){m=null;break}if(s=m.sibling,s!==null){s.return=m.return,m=s;break}m=m.return}s=m}}function $l(e,t,a,l){e=null;for(var s=t,c=!1;s!==null;){if(!c){if((s.flags&524288)!==0)c=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var m=s.alternate;if(m===null)throw Error(i(387));if(m=m.memoizedProps,m!==null){var y=s.type;ln(s.pendingProps.value,m.value)||(e!==null?e.push(y):e=[y])}}else if(s===Se.current){if(m=s.alternate,m===null)throw Error(i(387));m.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(po):e=[po])}s=s.return}e!==null&&Nu(t,e,a,l),t.flags|=262144}function pi(e){for(e=e.firstContext;e!==null;){if(!ln(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function lr(e){rr=e,ta=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function It(e){return jp(rr,e)}function mi(e,t){return rr===null&&lr(e),jp(e,t)}function jp(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},ta===null){if(e===null)throw Error(i(308));ta=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ta=ta.next=t;return a}var ab=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},rb=n.unstable_scheduleCallback,lb=n.unstable_NormalPriority,Bt={$$typeof:B,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function _u(){return{controller:new ab,data:new Map,refCount:0}}function Ll(e){e.refCount--,e.refCount===0&&rb(lb,function(){e.controller.abort()})}var Ul=null,$u=0,qr=0,Pr=null;function ob(e,t){if(Ul===null){var a=Ul=[];$u=0,qr=Uc(),Pr={status:"pending",value:void 0,then:function(l){a.push(l)}}}return $u++,t.then(Bp,Bp),t}function Bp(){if(--$u===0&&Ul!==null){Pr!==null&&(Pr.status="fulfilled");var e=Ul;Ul=null,qr=0,Pr=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function ib(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var s=0;s<a.length;s++)(0,a[s])(t)},function(s){for(l.status="rejected",l.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),l}var Dp=w.S;w.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&ob(e,t),Dp!==null&&Dp(e,t)};var or=X(null);function Lu(){var e=or.current;return e!==null?e:ct.pooledCache}function hi(e,t){t===null?re(or,or.current):re(or,t.pool)}function Np(){var e=Lu();return e===null?null:{parent:Bt._currentValue,pool:e}}var Hl=Error(i(460)),_p=Error(i(474)),gi=Error(i(542)),Uu={then:function(){}};function $p(e){return e=e.status,e==="fulfilled"||e==="rejected"}function yi(){}function Lp(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(yi,yi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hp(e),e;default:if(typeof t.status=="string")t.then(yi,yi);else{if(e=ct,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=l}},function(l){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hp(e),e}throw ql=t,Hl}}var ql=null;function Up(){if(ql===null)throw Error(i(459));var e=ql;return ql=null,e}function Hp(e){if(e===Hl||e===gi)throw Error(i(483))}var Ta=!1;function Hu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ea(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Ra(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Fe&2)!==0){var s=l.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),l.pending=t,t=ui(e),Ap(e,null,a),t}return si(e,l,t,a),ui(e)}function Pl(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Bd(e,a)}}function Pu(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var s=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var m={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?s=c=m:c=c.next=m,a=a.next}while(a!==null);c===null?s=c=t:c=c.next=t}else s=c=t;a={baseState:l.baseState,firstBaseUpdate:s,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Vu=!1;function Vl(){if(Vu){var e=Pr;if(e!==null)throw e}}function Gl(e,t,a,l){Vu=!1;var s=e.updateQueue;Ta=!1;var c=s.firstBaseUpdate,m=s.lastBaseUpdate,y=s.shared.pending;if(y!==null){s.shared.pending=null;var z=y,H=z.next;z.next=null,m===null?c=H:m.next=H,m=z;var W=e.alternate;W!==null&&(W=W.updateQueue,y=W.lastBaseUpdate,y!==m&&(y===null?W.firstBaseUpdate=H:y.next=H,W.lastBaseUpdate=z))}if(c!==null){var ae=s.baseState;m=0,W=H=z=null,y=c;do{var q=y.lane&-536870913,V=q!==y.lane;if(V?(Le&q)===q:(l&q)===q){q!==0&&q===qr&&(Vu=!0),W!==null&&(W=W.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var be=e,ye=y;q=t;var ot=a;switch(ye.tag){case 1:if(be=ye.payload,typeof be=="function"){ae=be.call(ot,ae,q);break e}ae=be;break e;case 3:be.flags=be.flags&-65537|128;case 0:if(be=ye.payload,q=typeof be=="function"?be.call(ot,ae,q):be,q==null)break e;ae=v({},ae,q);break e;case 2:Ta=!0}}q=y.callback,q!==null&&(e.flags|=64,V&&(e.flags|=8192),V=s.callbacks,V===null?s.callbacks=[q]:V.push(q))}else V={lane:q,tag:y.tag,payload:y.payload,callback:y.callback,next:null},W===null?(H=W=V,z=ae):W=W.next=V,m|=q;if(y=y.next,y===null){if(y=s.shared.pending,y===null)break;V=y,y=V.next,V.next=null,s.lastBaseUpdate=V,s.shared.pending=null}}while(!0);W===null&&(z=ae),s.baseState=z,s.firstBaseUpdate=H,s.lastBaseUpdate=W,c===null&&(s.shared.lanes=0),Ba|=m,e.lanes=m,e.memoizedState=ae}}function qp(e,t){if(typeof e!="function")throw Error(i(191,e));e.call(t)}function Pp(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)qp(a[e],t)}var Vr=X(null),vi=X(0);function Vp(e,t){e=ua,re(vi,e),re(Vr,t),ua=e|t.baseLanes}function Gu(){re(vi,ua),re(Vr,Vr.current)}function Yu(){ua=vi.current,le(Vr),le(vi)}var Aa=0,Ae=null,rt=null,wt=null,bi=!1,Gr=!1,ir=!1,Si=0,Yl=0,Yr=null,sb=0;function At(){throw Error(i(321))}function Iu(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!ln(e[a],t[a]))return!1;return!0}function Xu(e,t,a,l,s,c){return Aa=c,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,w.H=e===null||e.memoizedState===null?Am:Mm,ir=!1,c=a(l,s),ir=!1,Gr&&(c=Yp(t,a,l,s)),Gp(e),c}function Gp(e){w.H=Ai;var t=rt!==null&&rt.next!==null;if(Aa=0,wt=rt=Ae=null,bi=!1,Yl=0,Yr=null,t)throw Error(i(300));e===null||_t||(e=e.dependencies,e!==null&&pi(e)&&(_t=!0))}function Yp(e,t,a,l){Ae=e;var s=0;do{if(Gr&&(Yr=null),Yl=0,Gr=!1,25<=s)throw Error(i(301));if(s+=1,wt=rt=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}w.H=hb,c=t(a,l)}while(Gr);return c}function ub(){var e=w.H,t=e.useState()[0];return t=typeof t.then=="function"?Il(t):t,e=e.useState()[0],(rt!==null?rt.memoizedState:null)!==e&&(Ae.flags|=1024),t}function Ku(){var e=Si!==0;return Si=0,e}function Qu(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Fu(e){if(bi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}bi=!1}Aa=0,wt=rt=Ae=null,Gr=!1,Yl=Si=0,Yr=null}function en(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return wt===null?Ae.memoizedState=wt=e:wt=wt.next=e,wt}function zt(){if(rt===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=rt.next;var t=wt===null?Ae.memoizedState:wt.next;if(t!==null)wt=t,rt=e;else{if(e===null)throw Ae.alternate===null?Error(i(467)):Error(i(310));rt=e,e={memoizedState:rt.memoizedState,baseState:rt.baseState,baseQueue:rt.baseQueue,queue:rt.queue,next:null},wt===null?Ae.memoizedState=wt=e:wt=wt.next=e}return wt}function Zu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Il(e){var t=Yl;return Yl+=1,Yr===null&&(Yr=[]),e=Lp(Yr,e,t),t=Ae,(wt===null?t.memoizedState:wt.next)===null&&(t=t.alternate,w.H=t===null||t.memoizedState===null?Am:Mm),e}function xi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Il(e);if(e.$$typeof===B)return It(e)}throw Error(i(438,String(e)))}function Wu(e){var t=null,a=Ae.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=Ae.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Zu(),Ae.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=J;return t.index++,a}function aa(e,t){return typeof t=="function"?t(e):t}function Ci(e){var t=zt();return Ju(t,rt,e)}function Ju(e,t,a){var l=e.queue;if(l===null)throw Error(i(311));l.lastRenderedReducer=a;var s=e.baseQueue,c=l.pending;if(c!==null){if(s!==null){var m=s.next;s.next=c.next,c.next=m}t.baseQueue=s=c,l.pending=null}if(c=e.baseState,s===null)e.memoizedState=c;else{t=s.next;var y=m=null,z=null,H=t,W=!1;do{var ae=H.lane&-536870913;if(ae!==H.lane?(Le&ae)===ae:(Aa&ae)===ae){var q=H.revertLane;if(q===0)z!==null&&(z=z.next={lane:0,revertLane:0,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null}),ae===qr&&(W=!0);else if((Aa&q)===q){H=H.next,q===qr&&(W=!0);continue}else ae={lane:0,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},z===null?(y=z=ae,m=c):z=z.next=ae,Ae.lanes|=q,Ba|=q;ae=H.action,ir&&a(c,ae),c=H.hasEagerState?H.eagerState:a(c,ae)}else q={lane:ae,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},z===null?(y=z=q,m=c):z=z.next=q,Ae.lanes|=ae,Ba|=ae;H=H.next}while(H!==null&&H!==t);if(z===null?m=c:z.next=y,!ln(c,e.memoizedState)&&(_t=!0,W&&(a=Pr,a!==null)))throw a;e.memoizedState=c,e.baseState=m,e.baseQueue=z,l.lastRenderedState=c}return s===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function ec(e){var t=zt(),a=t.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var l=a.dispatch,s=a.pending,c=t.memoizedState;if(s!==null){a.pending=null;var m=s=s.next;do c=e(c,m.action),m=m.next;while(m!==s);ln(c,t.memoizedState)||(_t=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,l]}function Ip(e,t,a){var l=Ae,s=zt(),c=Pe;if(c){if(a===void 0)throw Error(i(407));a=a()}else a=t();var m=!ln((rt||s).memoizedState,a);m&&(s.memoizedState=a,_t=!0),s=s.queue;var y=Qp.bind(null,l,s,e);if(Xl(2048,8,y,[e]),s.getSnapshot!==t||m||wt!==null&&wt.memoizedState.tag&1){if(l.flags|=2048,Ir(9,Ti(),Kp.bind(null,l,s,a,t),null),ct===null)throw Error(i(349));c||(Aa&124)!==0||Xp(l,t,a)}return a}function Xp(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Ae.updateQueue,t===null?(t=Zu(),Ae.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Kp(e,t,a,l){t.value=a,t.getSnapshot=l,Fp(t)&&Zp(e)}function Qp(e,t,a){return a(function(){Fp(t)&&Zp(e)})}function Fp(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!ln(e,a)}catch{return!0}}function Zp(e){var t=$r(e,2);t!==null&&dn(t,e,2)}function tc(e){var t=en();if(typeof e=="function"){var a=e;if(e=a(),ir){nt(!0);try{a()}finally{nt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:aa,lastRenderedState:e},t}function Wp(e,t,a,l){return e.baseState=a,Ju(e,rt,typeof l=="function"?l:aa)}function cb(e,t,a,l,s){if(Ri(e))throw Error(i(485));if(e=t.action,e!==null){var c={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){c.listeners.push(m)}};w.T!==null?a(!0):c.isTransition=!1,l(c),a=t.pending,a===null?(c.next=t.pending=c,Jp(t,c)):(c.next=a.next,t.pending=a.next=c)}}function Jp(e,t){var a=t.action,l=t.payload,s=e.state;if(t.isTransition){var c=w.T,m={};w.T=m;try{var y=a(s,l),z=w.S;z!==null&&z(m,y),em(e,t,y)}catch(H){nc(e,t,H)}finally{w.T=c}}else try{c=a(s,l),em(e,t,c)}catch(H){nc(e,t,H)}}function em(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){tm(e,t,l)},function(l){return nc(e,t,l)}):tm(e,t,a)}function tm(e,t,a){t.status="fulfilled",t.value=a,nm(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Jp(e,a)))}function nc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,nm(t),t=t.next;while(t!==l)}e.action=null}function nm(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function am(e,t){return t}function rm(e,t){if(Pe){var a=ct.formState;if(a!==null){e:{var l=Ae;if(Pe){if(Ct){t:{for(var s=Ct,c=Dn;s.nodeType!==8;){if(!c){s=null;break t}if(s=kn(s.nextSibling),s===null){s=null;break t}}c=s.data,s=c==="F!"||c==="F"?s:null}if(s){Ct=kn(s.nextSibling),l=s.data==="F!";break e}}ar(l)}l=!1}l&&(t=a[0])}}return a=en(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:am,lastRenderedState:t},a.queue=l,a=Tm.bind(null,Ae,l),l.dispatch=a,l=tc(!1),c=ic.bind(null,Ae,!1,l.queue),l=en(),s={state:t,dispatch:null,action:e,pending:null},l.queue=s,a=cb.bind(null,Ae,s,c,a),s.dispatch=a,l.memoizedState=e,[t,a,!1]}function lm(e){var t=zt();return om(t,rt,e)}function om(e,t,a){if(t=Ju(e,t,am)[0],e=Ci(aa)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Il(t)}catch(m){throw m===Hl?gi:m}else l=t;t=zt();var s=t.queue,c=s.dispatch;return a!==t.memoizedState&&(Ae.flags|=2048,Ir(9,Ti(),fb.bind(null,s,a),null)),[l,c,e]}function fb(e,t){e.action=t}function im(e){var t=zt(),a=rt;if(a!==null)return om(t,a,e);zt(),t=t.memoizedState,a=zt();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Ir(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=Ae.updateQueue,t===null&&(t=Zu(),Ae.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Ti(){return{destroy:void 0,resource:void 0}}function sm(){return zt().memoizedState}function Ei(e,t,a,l){var s=en();l=l===void 0?null:l,Ae.flags|=e,s.memoizedState=Ir(1|t,Ti(),a,l)}function Xl(e,t,a,l){var s=zt();l=l===void 0?null:l;var c=s.memoizedState.inst;rt!==null&&l!==null&&Iu(l,rt.memoizedState.deps)?s.memoizedState=Ir(t,c,a,l):(Ae.flags|=e,s.memoizedState=Ir(1|t,c,a,l))}function um(e,t){Ei(8390656,8,e,t)}function cm(e,t){Xl(2048,8,e,t)}function fm(e,t){return Xl(4,2,e,t)}function dm(e,t){return Xl(4,4,e,t)}function pm(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mm(e,t,a){a=a!=null?a.concat([e]):null,Xl(4,4,pm.bind(null,t,e),a)}function ac(){}function hm(e,t){var a=zt();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Iu(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function gm(e,t){var a=zt();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Iu(t,l[1]))return l[0];if(l=e(),ir){nt(!0);try{e()}finally{nt(!1)}}return a.memoizedState=[l,t],l}function rc(e,t,a){return a===void 0||(Aa&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=bh(),Ae.lanes|=e,Ba|=e,a)}function ym(e,t,a,l){return ln(a,t)?a:Vr.current!==null?(e=rc(e,a,l),ln(e,t)||(_t=!0),e):(Aa&42)===0?(_t=!0,e.memoizedState=a):(e=bh(),Ae.lanes|=e,Ba|=e,t)}function vm(e,t,a,l,s){var c=K.p;K.p=c!==0&&8>c?c:8;var m=w.T,y={};w.T=y,ic(e,!1,t,a);try{var z=s(),H=w.S;if(H!==null&&H(y,z),z!==null&&typeof z=="object"&&typeof z.then=="function"){var W=ib(z,l);Kl(e,t,W,fn(e))}else Kl(e,t,l,fn(e))}catch(ae){Kl(e,t,{then:function(){},status:"rejected",reason:ae},fn())}finally{K.p=c,w.T=m}}function db(){}function lc(e,t,a,l){if(e.tag!==5)throw Error(i(476));var s=bm(e).queue;vm(e,s,t,oe,a===null?db:function(){return Sm(e),a(l)})}function bm(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:oe,baseState:oe,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:aa,lastRenderedState:oe},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:aa,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Sm(e){var t=bm(e).next.queue;Kl(e,t,{},fn())}function oc(){return It(po)}function xm(){return zt().memoizedState}function Cm(){return zt().memoizedState}function pb(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=fn();e=Ea(a);var l=Ra(t,e,a);l!==null&&(dn(l,t,a),Pl(l,t,a)),t={cache:_u()},e.payload=t;return}t=t.return}}function mb(e,t,a){var l=fn();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Ri(e)?Em(t,a):(a=Au(e,t,a,l),a!==null&&(dn(a,e,l),Rm(a,t,l)))}function Tm(e,t,a){var l=fn();Kl(e,t,a,l)}function Kl(e,t,a,l){var s={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Ri(e))Em(t,s);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var m=t.lastRenderedState,y=c(m,a);if(s.hasEagerState=!0,s.eagerState=y,ln(y,m))return si(e,t,s,0),ct===null&&ii(),!1}catch{}finally{}if(a=Au(e,t,s,l),a!==null)return dn(a,e,l),Rm(a,t,l),!0}return!1}function ic(e,t,a,l){if(l={lane:2,revertLane:Uc(),action:l,hasEagerState:!1,eagerState:null,next:null},Ri(e)){if(t)throw Error(i(479))}else t=Au(e,a,l,2),t!==null&&dn(t,e,2)}function Ri(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function Em(e,t){Gr=bi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Rm(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Bd(e,a)}}var Ai={readContext:It,use:xi,useCallback:At,useContext:At,useEffect:At,useImperativeHandle:At,useLayoutEffect:At,useInsertionEffect:At,useMemo:At,useReducer:At,useRef:At,useState:At,useDebugValue:At,useDeferredValue:At,useTransition:At,useSyncExternalStore:At,useId:At,useHostTransitionStatus:At,useFormState:At,useActionState:At,useOptimistic:At,useMemoCache:At,useCacheRefresh:At},Am={readContext:It,use:xi,useCallback:function(e,t){return en().memoizedState=[e,t===void 0?null:t],e},useContext:It,useEffect:um,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ei(4194308,4,pm.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ei(4194308,4,e,t)},useInsertionEffect:function(e,t){Ei(4,2,e,t)},useMemo:function(e,t){var a=en();t=t===void 0?null:t;var l=e();if(ir){nt(!0);try{e()}finally{nt(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=en();if(a!==void 0){var s=a(t);if(ir){nt(!0);try{a(t)}finally{nt(!1)}}}else s=t;return l.memoizedState=l.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},l.queue=e,e=e.dispatch=mb.bind(null,Ae,e),[l.memoizedState,e]},useRef:function(e){var t=en();return e={current:e},t.memoizedState=e},useState:function(e){e=tc(e);var t=e.queue,a=Tm.bind(null,Ae,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:ac,useDeferredValue:function(e,t){var a=en();return rc(a,e,t)},useTransition:function(){var e=tc(!1);return e=vm.bind(null,Ae,e.queue,!0,!1),en().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=Ae,s=en();if(Pe){if(a===void 0)throw Error(i(407));a=a()}else{if(a=t(),ct===null)throw Error(i(349));(Le&124)!==0||Xp(l,t,a)}s.memoizedState=a;var c={value:a,getSnapshot:t};return s.queue=c,um(Qp.bind(null,l,c,e),[e]),l.flags|=2048,Ir(9,Ti(),Kp.bind(null,l,c,a,t),null),a},useId:function(){var e=en(),t=ct.identifierPrefix;if(Pe){var a=ea,l=Jn;a=(l&~(1<<32-qe(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Si++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=sb++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:oc,useFormState:rm,useActionState:rm,useOptimistic:function(e){var t=en();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=ic.bind(null,Ae,!0,a),a.dispatch=t,[e,t]},useMemoCache:Wu,useCacheRefresh:function(){return en().memoizedState=pb.bind(null,Ae)}},Mm={readContext:It,use:xi,useCallback:hm,useContext:It,useEffect:cm,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:gm,useReducer:Ci,useRef:sm,useState:function(){return Ci(aa)},useDebugValue:ac,useDeferredValue:function(e,t){var a=zt();return ym(a,rt.memoizedState,e,t)},useTransition:function(){var e=Ci(aa)[0],t=zt().memoizedState;return[typeof e=="boolean"?e:Il(e),t]},useSyncExternalStore:Ip,useId:xm,useHostTransitionStatus:oc,useFormState:lm,useActionState:lm,useOptimistic:function(e,t){var a=zt();return Wp(a,rt,e,t)},useMemoCache:Wu,useCacheRefresh:Cm},hb={readContext:It,use:xi,useCallback:hm,useContext:It,useEffect:cm,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:gm,useReducer:ec,useRef:sm,useState:function(){return ec(aa)},useDebugValue:ac,useDeferredValue:function(e,t){var a=zt();return rt===null?rc(a,e,t):ym(a,rt.memoizedState,e,t)},useTransition:function(){var e=ec(aa)[0],t=zt().memoizedState;return[typeof e=="boolean"?e:Il(e),t]},useSyncExternalStore:Ip,useId:xm,useHostTransitionStatus:oc,useFormState:im,useActionState:im,useOptimistic:function(e,t){var a=zt();return rt!==null?Wp(a,rt,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Wu,useCacheRefresh:Cm},Xr=null,Ql=0;function Mi(e){var t=Ql;return Ql+=1,Xr===null&&(Xr=[]),Lp(Xr,e,t)}function Fl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Oi(e,t){throw t.$$typeof===x?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Om(e){var t=e._init;return t(e._payload)}function wm(e){function t($,D){if(e){var L=$.deletions;L===null?($.deletions=[D],$.flags|=16):L.push(D)}}function a($,D){if(!e)return null;for(;D!==null;)t($,D),D=D.sibling;return null}function l($){for(var D=new Map;$!==null;)$.key!==null?D.set($.key,$):D.set($.index,$),$=$.sibling;return D}function s($,D){return $=Wn($,D),$.index=0,$.sibling=null,$}function c($,D,L){return $.index=L,e?(L=$.alternate,L!==null?(L=L.index,L<D?($.flags|=67108866,D):L):($.flags|=67108866,D)):($.flags|=1048576,D)}function m($){return e&&$.alternate===null&&($.flags|=67108866),$}function y($,D,L,ee){return D===null||D.tag!==6?(D=Ou(L,$.mode,ee),D.return=$,D):(D=s(D,L),D.return=$,D)}function z($,D,L,ee){var de=L.type;return de===T?W($,D,L.props.children,ee,L.key):D!==null&&(D.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===I&&Om(de)===D.type)?(D=s(D,L.props),Fl(D,L),D.return=$,D):(D=ci(L.type,L.key,L.props,null,$.mode,ee),Fl(D,L),D.return=$,D)}function H($,D,L,ee){return D===null||D.tag!==4||D.stateNode.containerInfo!==L.containerInfo||D.stateNode.implementation!==L.implementation?(D=wu(L,$.mode,ee),D.return=$,D):(D=s(D,L.children||[]),D.return=$,D)}function W($,D,L,ee,de){return D===null||D.tag!==7?(D=Ja(L,$.mode,ee,de),D.return=$,D):(D=s(D,L),D.return=$,D)}function ae($,D,L){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return D=Ou(""+D,$.mode,L),D.return=$,D;if(typeof D=="object"&&D!==null){switch(D.$$typeof){case R:return L=ci(D.type,D.key,D.props,null,$.mode,L),Fl(L,D),L.return=$,L;case A:return D=wu(D,$.mode,L),D.return=$,D;case I:var ee=D._init;return D=ee(D._payload),ae($,D,L)}if(G(D)||b(D))return D=Ja(D,$.mode,L,null),D.return=$,D;if(typeof D.then=="function")return ae($,Mi(D),L);if(D.$$typeof===B)return ae($,mi($,D),L);Oi($,D)}return null}function q($,D,L,ee){var de=D!==null?D.key:null;if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return de!==null?null:y($,D,""+L,ee);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case R:return L.key===de?z($,D,L,ee):null;case A:return L.key===de?H($,D,L,ee):null;case I:return de=L._init,L=de(L._payload),q($,D,L,ee)}if(G(L)||b(L))return de!==null?null:W($,D,L,ee,null);if(typeof L.then=="function")return q($,D,Mi(L),ee);if(L.$$typeof===B)return q($,D,mi($,L),ee);Oi($,L)}return null}function V($,D,L,ee,de){if(typeof ee=="string"&&ee!==""||typeof ee=="number"||typeof ee=="bigint")return $=$.get(L)||null,y(D,$,""+ee,de);if(typeof ee=="object"&&ee!==null){switch(ee.$$typeof){case R:return $=$.get(ee.key===null?L:ee.key)||null,z(D,$,ee,de);case A:return $=$.get(ee.key===null?L:ee.key)||null,H(D,$,ee,de);case I:var Oe=ee._init;return ee=Oe(ee._payload),V($,D,L,ee,de)}if(G(ee)||b(ee))return $=$.get(L)||null,W(D,$,ee,de,null);if(typeof ee.then=="function")return V($,D,L,Mi(ee),de);if(ee.$$typeof===B)return V($,D,L,mi(D,ee),de);Oi(D,ee)}return null}function be($,D,L,ee){for(var de=null,Oe=null,me=D,ve=D=0,Lt=null;me!==null&&ve<L.length;ve++){me.index>ve?(Lt=me,me=null):Lt=me.sibling;var Ue=q($,me,L[ve],ee);if(Ue===null){me===null&&(me=Lt);break}e&&me&&Ue.alternate===null&&t($,me),D=c(Ue,D,ve),Oe===null?de=Ue:Oe.sibling=Ue,Oe=Ue,me=Lt}if(ve===L.length)return a($,me),Pe&&tr($,ve),de;if(me===null){for(;ve<L.length;ve++)me=ae($,L[ve],ee),me!==null&&(D=c(me,D,ve),Oe===null?de=me:Oe.sibling=me,Oe=me);return Pe&&tr($,ve),de}for(me=l(me);ve<L.length;ve++)Lt=V(me,$,ve,L[ve],ee),Lt!==null&&(e&&Lt.alternate!==null&&me.delete(Lt.key===null?ve:Lt.key),D=c(Lt,D,ve),Oe===null?de=Lt:Oe.sibling=Lt,Oe=Lt);return e&&me.forEach(function(Pa){return t($,Pa)}),Pe&&tr($,ve),de}function ye($,D,L,ee){if(L==null)throw Error(i(151));for(var de=null,Oe=null,me=D,ve=D=0,Lt=null,Ue=L.next();me!==null&&!Ue.done;ve++,Ue=L.next()){me.index>ve?(Lt=me,me=null):Lt=me.sibling;var Pa=q($,me,Ue.value,ee);if(Pa===null){me===null&&(me=Lt);break}e&&me&&Pa.alternate===null&&t($,me),D=c(Pa,D,ve),Oe===null?de=Pa:Oe.sibling=Pa,Oe=Pa,me=Lt}if(Ue.done)return a($,me),Pe&&tr($,ve),de;if(me===null){for(;!Ue.done;ve++,Ue=L.next())Ue=ae($,Ue.value,ee),Ue!==null&&(D=c(Ue,D,ve),Oe===null?de=Ue:Oe.sibling=Ue,Oe=Ue);return Pe&&tr($,ve),de}for(me=l(me);!Ue.done;ve++,Ue=L.next())Ue=V(me,$,ve,Ue.value,ee),Ue!==null&&(e&&Ue.alternate!==null&&me.delete(Ue.key===null?ve:Ue.key),D=c(Ue,D,ve),Oe===null?de=Ue:Oe.sibling=Ue,Oe=Ue);return e&&me.forEach(function(g1){return t($,g1)}),Pe&&tr($,ve),de}function ot($,D,L,ee){if(typeof L=="object"&&L!==null&&L.type===T&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case R:e:{for(var de=L.key;D!==null;){if(D.key===de){if(de=L.type,de===T){if(D.tag===7){a($,D.sibling),ee=s(D,L.props.children),ee.return=$,$=ee;break e}}else if(D.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===I&&Om(de)===D.type){a($,D.sibling),ee=s(D,L.props),Fl(ee,L),ee.return=$,$=ee;break e}a($,D);break}else t($,D);D=D.sibling}L.type===T?(ee=Ja(L.props.children,$.mode,ee,L.key),ee.return=$,$=ee):(ee=ci(L.type,L.key,L.props,null,$.mode,ee),Fl(ee,L),ee.return=$,$=ee)}return m($);case A:e:{for(de=L.key;D!==null;){if(D.key===de)if(D.tag===4&&D.stateNode.containerInfo===L.containerInfo&&D.stateNode.implementation===L.implementation){a($,D.sibling),ee=s(D,L.children||[]),ee.return=$,$=ee;break e}else{a($,D);break}else t($,D);D=D.sibling}ee=wu(L,$.mode,ee),ee.return=$,$=ee}return m($);case I:return de=L._init,L=de(L._payload),ot($,D,L,ee)}if(G(L))return be($,D,L,ee);if(b(L)){if(de=b(L),typeof de!="function")throw Error(i(150));return L=de.call(L),ye($,D,L,ee)}if(typeof L.then=="function")return ot($,D,Mi(L),ee);if(L.$$typeof===B)return ot($,D,mi($,L),ee);Oi($,L)}return typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint"?(L=""+L,D!==null&&D.tag===6?(a($,D.sibling),ee=s(D,L),ee.return=$,$=ee):(a($,D),ee=Ou(L,$.mode,ee),ee.return=$,$=ee),m($)):a($,D)}return function($,D,L,ee){try{Ql=0;var de=ot($,D,L,ee);return Xr=null,de}catch(me){if(me===Hl||me===gi)throw me;var Oe=on(29,me,null,$.mode);return Oe.lanes=ee,Oe.return=$,Oe}finally{}}}var Kr=wm(!0),zm=wm(!1),Cn=X(null),Nn=null;function Ma(e){var t=e.alternate;re(Dt,Dt.current&1),re(Cn,e),Nn===null&&(t===null||Vr.current!==null||t.memoizedState!==null)&&(Nn=e)}function km(e){if(e.tag===22){if(re(Dt,Dt.current),re(Cn,e),Nn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Nn=e)}}else Oa()}function Oa(){re(Dt,Dt.current),re(Cn,Cn.current)}function ra(e){le(Cn),Nn===e&&(Nn=null),le(Dt)}var Dt=X(0);function wi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Zc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function sc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:v({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var uc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=fn(),s=Ea(l);s.payload=t,a!=null&&(s.callback=a),t=Ra(e,s,l),t!==null&&(dn(t,e,l),Pl(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=fn(),s=Ea(l);s.tag=1,s.payload=t,a!=null&&(s.callback=a),t=Ra(e,s,l),t!==null&&(dn(t,e,l),Pl(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=fn(),l=Ea(a);l.tag=2,t!=null&&(l.callback=t),t=Ra(e,l,a),t!==null&&(dn(t,e,a),Pl(t,e,a))}};function jm(e,t,a,l,s,c,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,m):t.prototype&&t.prototype.isPureReactComponent?!jl(a,l)||!jl(s,c):!0}function Bm(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&uc.enqueueReplaceState(t,t.state,null)}function sr(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=v({},a));for(var s in e)a[s]===void 0&&(a[s]=e[s])}return a}var zi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Dm(e){zi(e)}function Nm(e){console.error(e)}function _m(e){zi(e)}function ki(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function $m(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function cc(e,t,a){return a=Ea(a),a.tag=3,a.payload={element:null},a.callback=function(){ki(e,t)},a}function Lm(e){return e=Ea(e),e.tag=3,e}function Um(e,t,a,l){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var c=l.value;e.payload=function(){return s(c)},e.callback=function(){$m(t,a,l)}}var m=a.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(e.callback=function(){$m(t,a,l),typeof s!="function"&&(Da===null?Da=new Set([this]):Da.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function gb(e,t,a,l,s){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&$l(t,a,s,!0),a=Cn.current,a!==null){switch(a.tag){case 13:return Nn===null?Dc():a.alternate===null&&Tt===0&&(Tt=3),a.flags&=-257,a.flags|=65536,a.lanes=s,l===Uu?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),_c(e,l,s)),!1;case 22:return a.flags|=65536,l===Uu?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),_c(e,l,s)),!1}throw Error(i(435,a.tag))}return _c(e,l,s),Dc(),!1}if(Pe)return t=Cn.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,l!==ju&&(e=Error(i(422),{cause:l}),_l(vn(e,a)))):(l!==ju&&(t=Error(i(423),{cause:l}),_l(vn(t,a))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,l=vn(l,a),s=cc(e.stateNode,l,s),Pu(e,s),Tt!==4&&(Tt=2)),!1;var c=Error(i(520),{cause:l});if(c=vn(c,a),ao===null?ao=[c]:ao.push(c),Tt!==4&&(Tt=2),t===null)return!0;l=vn(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=s&-s,a.lanes|=e,e=cc(a.stateNode,l,e),Pu(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Da===null||!Da.has(c))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Lm(s),Um(s,e,a,l),Pu(a,s),!1}a=a.return}while(a!==null);return!1}var Hm=Error(i(461)),_t=!1;function qt(e,t,a,l){t.child=e===null?zm(t,null,a,l):Kr(t,e.child,a,l)}function qm(e,t,a,l,s){a=a.render;var c=t.ref;if("ref"in l){var m={};for(var y in l)y!=="ref"&&(m[y]=l[y])}else m=l;return lr(t),l=Xu(e,t,a,m,c,s),y=Ku(),e!==null&&!_t?(Qu(e,t,s),la(e,t,s)):(Pe&&y&&zu(t),t.flags|=1,qt(e,t,l,s),t.child)}function Pm(e,t,a,l,s){if(e===null){var c=a.type;return typeof c=="function"&&!Mu(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,Vm(e,t,c,l,s)):(e=ci(a.type,null,l,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!vc(e,s)){var m=c.memoizedProps;if(a=a.compare,a=a!==null?a:jl,a(m,l)&&e.ref===t.ref)return la(e,t,s)}return t.flags|=1,e=Wn(c,l),e.ref=t.ref,e.return=t,t.child=e}function Vm(e,t,a,l,s){if(e!==null){var c=e.memoizedProps;if(jl(c,l)&&e.ref===t.ref)if(_t=!1,t.pendingProps=l=c,vc(e,s))(e.flags&131072)!==0&&(_t=!0);else return t.lanes=e.lanes,la(e,t,s)}return fc(e,t,a,l,s)}function Gm(e,t,a){var l=t.pendingProps,s=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|a:a,e!==null){for(s=t.child=e.child,c=0;s!==null;)c=c|s.lanes|s.childLanes,s=s.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return Ym(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&hi(t,c!==null?c.cachePool:null),c!==null?Vp(t,c):Gu(),km(t);else return t.lanes=t.childLanes=536870912,Ym(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?(hi(t,c.cachePool),Vp(t,c),Oa(),t.memoizedState=null):(e!==null&&hi(t,null),Gu(),Oa());return qt(e,t,s,a),t.child}function Ym(e,t,a,l){var s=Lu();return s=s===null?null:{parent:Bt._currentValue,pool:s},t.memoizedState={baseLanes:a,cachePool:s},e!==null&&hi(t,null),Gu(),km(t),e!==null&&$l(e,t,l,!0),null}function ji(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(i(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function fc(e,t,a,l,s){return lr(t),a=Xu(e,t,a,l,void 0,s),l=Ku(),e!==null&&!_t?(Qu(e,t,s),la(e,t,s)):(Pe&&l&&zu(t),t.flags|=1,qt(e,t,a,s),t.child)}function Im(e,t,a,l,s,c){return lr(t),t.updateQueue=null,a=Yp(t,l,a,s),Gp(e),l=Ku(),e!==null&&!_t?(Qu(e,t,c),la(e,t,c)):(Pe&&l&&zu(t),t.flags|=1,qt(e,t,a,c),t.child)}function Xm(e,t,a,l,s){if(lr(t),t.stateNode===null){var c=Lr,m=a.contextType;typeof m=="object"&&m!==null&&(c=It(m)),c=new a(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=uc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},Hu(t),m=a.contextType,c.context=typeof m=="object"&&m!==null?It(m):Lr,c.state=t.memoizedState,m=a.getDerivedStateFromProps,typeof m=="function"&&(sc(t,a,m,l),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(m=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),m!==c.state&&uc.enqueueReplaceState(c,c.state,null),Gl(t,l,c,s),Vl(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var y=t.memoizedProps,z=sr(a,y);c.props=z;var H=c.context,W=a.contextType;m=Lr,typeof W=="object"&&W!==null&&(m=It(W));var ae=a.getDerivedStateFromProps;W=typeof ae=="function"||typeof c.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,W||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y||H!==m)&&Bm(t,c,l,m),Ta=!1;var q=t.memoizedState;c.state=q,Gl(t,l,c,s),Vl(),H=t.memoizedState,y||q!==H||Ta?(typeof ae=="function"&&(sc(t,a,ae,l),H=t.memoizedState),(z=Ta||jm(t,a,z,l,q,H,m))?(W||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=H),c.props=l,c.state=H,c.context=m,l=z):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,qu(e,t),m=t.memoizedProps,W=sr(a,m),c.props=W,ae=t.pendingProps,q=c.context,H=a.contextType,z=Lr,typeof H=="object"&&H!==null&&(z=It(H)),y=a.getDerivedStateFromProps,(H=typeof y=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m!==ae||q!==z)&&Bm(t,c,l,z),Ta=!1,q=t.memoizedState,c.state=q,Gl(t,l,c,s),Vl();var V=t.memoizedState;m!==ae||q!==V||Ta||e!==null&&e.dependencies!==null&&pi(e.dependencies)?(typeof y=="function"&&(sc(t,a,y,l),V=t.memoizedState),(W=Ta||jm(t,a,W,l,q,V,z)||e!==null&&e.dependencies!==null&&pi(e.dependencies))?(H||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,V,z),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,V,z)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&q===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&q===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=V),c.props=l,c.state=V,c.context=z,l=W):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&q===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&q===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,ji(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=Kr(t,e.child,null,s),t.child=Kr(t,null,a,s)):qt(e,t,a,s),t.memoizedState=c.state,e=t.child):e=la(e,t,s),e}function Km(e,t,a,l){return Nl(),t.flags|=256,qt(e,t,a,l),t.child}var dc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pc(e){return{baseLanes:e,cachePool:Np()}}function mc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Tn),e}function Qm(e,t,a){var l=t.pendingProps,s=!1,c=(t.flags&128)!==0,m;if((m=c)||(m=e!==null&&e.memoizedState===null?!1:(Dt.current&2)!==0),m&&(s=!0,t.flags&=-129),m=(t.flags&32)!==0,t.flags&=-33,e===null){if(Pe){if(s?Ma(t):Oa(),Pe){var y=Ct,z;if(z=y){e:{for(z=y,y=Dn;z.nodeType!==8;){if(!y){y=null;break e}if(z=kn(z.nextSibling),z===null){y=null;break e}}y=z}y!==null?(t.memoizedState={dehydrated:y,treeContext:er!==null?{id:Jn,overflow:ea}:null,retryLane:536870912,hydrationErrors:null},z=on(18,null,null,0),z.stateNode=y,z.return=t,t.child=z,Ft=t,Ct=null,z=!0):z=!1}z||ar(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Zc(y)?t.lanes=32:t.lanes=536870912,null;ra(t)}return y=l.children,l=l.fallback,s?(Oa(),s=t.mode,y=Bi({mode:"hidden",children:y},s),l=Ja(l,s,a,null),y.return=t,l.return=t,y.sibling=l,t.child=y,s=t.child,s.memoizedState=pc(a),s.childLanes=mc(e,m,a),t.memoizedState=dc,l):(Ma(t),hc(t,y))}if(z=e.memoizedState,z!==null&&(y=z.dehydrated,y!==null)){if(c)t.flags&256?(Ma(t),t.flags&=-257,t=gc(e,t,a)):t.memoizedState!==null?(Oa(),t.child=e.child,t.flags|=128,t=null):(Oa(),s=l.fallback,y=t.mode,l=Bi({mode:"visible",children:l.children},y),s=Ja(s,y,a,null),s.flags|=2,l.return=t,s.return=t,l.sibling=s,t.child=l,Kr(t,e.child,null,a),l=t.child,l.memoizedState=pc(a),l.childLanes=mc(e,m,a),t.memoizedState=dc,t=s);else if(Ma(t),Zc(y)){if(m=y.nextSibling&&y.nextSibling.dataset,m)var H=m.dgst;m=H,l=Error(i(419)),l.stack="",l.digest=m,_l({value:l,source:null,stack:null}),t=gc(e,t,a)}else if(_t||$l(e,t,a,!1),m=(a&e.childLanes)!==0,_t||m){if(m=ct,m!==null&&(l=a&-a,l=(l&42)!==0?1:Ws(l),l=(l&(m.suspendedLanes|a))!==0?0:l,l!==0&&l!==z.retryLane))throw z.retryLane=l,$r(e,l),dn(m,e,l),Hm;y.data==="$?"||Dc(),t=gc(e,t,a)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=z.treeContext,Ct=kn(y.nextSibling),Ft=t,Pe=!0,nr=null,Dn=!1,e!==null&&(Sn[xn++]=Jn,Sn[xn++]=ea,Sn[xn++]=er,Jn=e.id,ea=e.overflow,er=t),t=hc(t,l.children),t.flags|=4096);return t}return s?(Oa(),s=l.fallback,y=t.mode,z=e.child,H=z.sibling,l=Wn(z,{mode:"hidden",children:l.children}),l.subtreeFlags=z.subtreeFlags&65011712,H!==null?s=Wn(H,s):(s=Ja(s,y,a,null),s.flags|=2),s.return=t,l.return=t,l.sibling=s,t.child=l,l=s,s=t.child,y=e.child.memoizedState,y===null?y=pc(a):(z=y.cachePool,z!==null?(H=Bt._currentValue,z=z.parent!==H?{parent:H,pool:H}:z):z=Np(),y={baseLanes:y.baseLanes|a,cachePool:z}),s.memoizedState=y,s.childLanes=mc(e,m,a),t.memoizedState=dc,l):(Ma(t),a=e.child,e=a.sibling,a=Wn(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(m=t.deletions,m===null?(t.deletions=[e],t.flags|=16):m.push(e)),t.child=a,t.memoizedState=null,a)}function hc(e,t){return t=Bi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Bi(e,t){return e=on(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function gc(e,t,a){return Kr(t,e.child,null,a),e=hc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fm(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Du(e.return,t,a)}function yc(e,t,a,l,s){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:s}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=a,c.tailMode=s)}function Zm(e,t,a){var l=t.pendingProps,s=l.revealOrder,c=l.tail;if(qt(e,t,l.children,a),l=Dt.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Fm(e,a,t);else if(e.tag===19)Fm(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(re(Dt,l),s){case"forwards":for(a=t.child,s=null;a!==null;)e=a.alternate,e!==null&&wi(e)===null&&(s=a),a=a.sibling;a=s,a===null?(s=t.child,t.child=null):(s=a.sibling,a.sibling=null),yc(t,!1,s,a,c);break;case"backwards":for(a=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&wi(e)===null){t.child=s;break}e=s.sibling,s.sibling=a,a=s,s=e}yc(t,!0,a,null,c);break;case"together":yc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function la(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ba|=t.lanes,(a&t.childLanes)===0)if(e!==null){if($l(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,a=Wn(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Wn(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function vc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&pi(e)))}function yb(e,t,a){switch(t.tag){case 3:Ce(t,t.stateNode.containerInfo),Ca(t,Bt,e.memoizedState.cache),Nl();break;case 27:case 5:xe(t);break;case 4:Ce(t,t.stateNode.containerInfo);break;case 10:Ca(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Ma(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Qm(e,t,a):(Ma(t),e=la(e,t,a),e!==null?e.sibling:null);Ma(t);break;case 19:var s=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||($l(e,t,a,!1),l=(a&t.childLanes)!==0),s){if(l)return Zm(e,t,a);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),re(Dt,Dt.current),l)break;return null;case 22:case 23:return t.lanes=0,Gm(e,t,a);case 24:Ca(t,Bt,e.memoizedState.cache)}return la(e,t,a)}function Wm(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)_t=!0;else{if(!vc(e,a)&&(t.flags&128)===0)return _t=!1,yb(e,t,a);_t=(e.flags&131072)!==0}else _t=!1,Pe&&(t.flags&1048576)!==0&&Op(t,di,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,s=l._init;if(l=s(l._payload),t.type=l,typeof l=="function")Mu(l)?(e=sr(l,e),t.tag=1,t=Xm(null,t,l,e,a)):(t.tag=0,t=fc(null,t,l,e,a));else{if(l!=null){if(s=l.$$typeof,s===k){t.tag=11,t=qm(null,t,l,e,a);break e}else if(s===Y){t.tag=14,t=Pm(null,t,l,e,a);break e}}throw t=P(l)||l,Error(i(306,t,""))}}return t;case 0:return fc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,s=sr(l,t.pendingProps),Xm(e,t,l,s,a);case 3:e:{if(Ce(t,t.stateNode.containerInfo),e===null)throw Error(i(387));l=t.pendingProps;var c=t.memoizedState;s=c.element,qu(e,t),Gl(t,l,null,a);var m=t.memoizedState;if(l=m.cache,Ca(t,Bt,l),l!==c.cache&&Nu(t,[Bt],a,!0),Vl(),l=m.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:m.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Km(e,t,l,a);break e}else if(l!==s){s=vn(Error(i(424)),t),_l(s),t=Km(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ct=kn(e.firstChild),Ft=t,Pe=!0,nr=null,Dn=!0,a=zm(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Nl(),l===s){t=la(e,t,a);break e}qt(e,t,l,a)}t=t.child}return t;case 26:return ji(e,t),e===null?(a=ng(t.type,null,t.pendingProps,null))?t.memoizedState=a:Pe||(a=t.type,e=t.pendingProps,l=Xi(se.current).createElement(a),l[Yt]=t,l[Wt]=e,Vt(l,a,e),Nt(l),t.stateNode=l):t.memoizedState=ng(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return xe(t),e===null&&Pe&&(l=t.stateNode=Jh(t.type,t.pendingProps,se.current),Ft=t,Dn=!0,s=Ct,$a(t.type)?(Wc=s,Ct=kn(l.firstChild)):Ct=s),qt(e,t,t.pendingProps.children,a),ji(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Pe&&((s=l=Ct)&&(l=Yb(l,t.type,t.pendingProps,Dn),l!==null?(t.stateNode=l,Ft=t,Ct=kn(l.firstChild),Dn=!1,s=!0):s=!1),s||ar(t)),xe(t),s=t.type,c=t.pendingProps,m=e!==null?e.memoizedProps:null,l=c.children,Kc(s,c)?l=null:m!==null&&Kc(s,m)&&(t.flags|=32),t.memoizedState!==null&&(s=Xu(e,t,ub,null,null,a),po._currentValue=s),ji(e,t),qt(e,t,l,a),t.child;case 6:return e===null&&Pe&&((e=a=Ct)&&(a=Ib(a,t.pendingProps,Dn),a!==null?(t.stateNode=a,Ft=t,Ct=null,e=!0):e=!1),e||ar(t)),null;case 13:return Qm(e,t,a);case 4:return Ce(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Kr(t,null,l,a):qt(e,t,l,a),t.child;case 11:return qm(e,t,t.type,t.pendingProps,a);case 7:return qt(e,t,t.pendingProps,a),t.child;case 8:return qt(e,t,t.pendingProps.children,a),t.child;case 12:return qt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Ca(t,t.type,l.value),qt(e,t,l.children,a),t.child;case 9:return s=t.type._context,l=t.pendingProps.children,lr(t),s=It(s),l=l(s),t.flags|=1,qt(e,t,l,a),t.child;case 14:return Pm(e,t,t.type,t.pendingProps,a);case 15:return Vm(e,t,t.type,t.pendingProps,a);case 19:return Zm(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Bi(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Wn(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Gm(e,t,a);case 24:return lr(t),l=It(Bt),e===null?(s=Lu(),s===null&&(s=ct,c=_u(),s.pooledCache=c,c.refCount++,c!==null&&(s.pooledCacheLanes|=a),s=c),t.memoizedState={parent:l,cache:s},Hu(t),Ca(t,Bt,s)):((e.lanes&a)!==0&&(qu(e,t),Gl(t,null,null,a),Vl()),s=e.memoizedState,c=t.memoizedState,s.parent!==l?(s={parent:l,cache:l},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),Ca(t,Bt,l)):(l=c.cache,Ca(t,Bt,l),l!==s.cache&&Nu(t,[Bt],a,!0))),qt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function oa(e){e.flags|=4}function Jm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!ig(t)){if(t=Cn.current,t!==null&&((Le&4194048)===Le?Nn!==null:(Le&62914560)!==Le&&(Le&536870912)===0||t!==Nn))throw ql=Uu,_p;e.flags|=8192}}function Di(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?kd():536870912,e.lanes|=t,Wr|=t)}function Zl(e,t){if(!Pe)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function bt(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var s=e.child;s!==null;)a|=s.lanes|s.childLanes,l|=s.subtreeFlags&65011712,l|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)a|=s.lanes|s.childLanes,l|=s.subtreeFlags,l|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function vb(e,t,a){var l=t.pendingProps;switch(ku(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return bt(t),null;case 1:return bt(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),na(Bt),_e(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Dl(t)?oa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,kp())),bt(t),null;case 26:return a=t.memoizedState,e===null?(oa(t),a!==null?(bt(t),Jm(t,a)):(bt(t),t.flags&=-16777217)):a?a!==e.memoizedState?(oa(t),bt(t),Jm(t,a)):(bt(t),t.flags&=-16777217):(e.memoizedProps!==l&&oa(t),bt(t),t.flags&=-16777217),null;case 27:Me(t),a=se.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&oa(t);else{if(!l){if(t.stateNode===null)throw Error(i(166));return bt(t),null}e=ue.current,Dl(t)?wp(t):(e=Jh(s,l,a),t.stateNode=e,oa(t))}return bt(t),null;case 5:if(Me(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&oa(t);else{if(!l){if(t.stateNode===null)throw Error(i(166));return bt(t),null}if(e=ue.current,Dl(t))wp(t);else{switch(s=Xi(se.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?s.createElement("select",{is:l.is}):s.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?s.createElement(a,{is:l.is}):s.createElement(a)}}e[Yt]=t,e[Wt]=l;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(Vt(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&oa(t)}}return bt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&oa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(i(166));if(e=se.current,Dl(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,s=Ft,s!==null)switch(s.tag){case 27:case 5:l=s.memoizedProps}e[Yt]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Ih(e.nodeValue,a)),e||ar(t)}else e=Xi(e).createTextNode(l),e[Yt]=t,t.stateNode=e}return bt(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=Dl(t),l!==null&&l.dehydrated!==null){if(e===null){if(!s)throw Error(i(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(i(317));s[Yt]=t}else Nl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;bt(t),s=!1}else s=kp(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(ra(t),t):(ra(t),null)}if(ra(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,s=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(s=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==s&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Di(t,t.updateQueue),bt(t),null;case 4:return _e(),e===null&&Vc(t.stateNode.containerInfo),bt(t),null;case 10:return na(t.type),bt(t),null;case 19:if(le(Dt),s=t.memoizedState,s===null)return bt(t),null;if(l=(t.flags&128)!==0,c=s.rendering,c===null)if(l)Zl(s,!1);else{if(Tt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=wi(e),c!==null){for(t.flags|=128,Zl(s,!1),e=c.updateQueue,t.updateQueue=e,Di(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Mp(a,e),a=a.sibling;return re(Dt,Dt.current&1|2),t.child}e=e.sibling}s.tail!==null&&Qe()>$i&&(t.flags|=128,l=!0,Zl(s,!1),t.lanes=4194304)}else{if(!l)if(e=wi(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Di(t,e),Zl(s,!0),s.tail===null&&s.tailMode==="hidden"&&!c.alternate&&!Pe)return bt(t),null}else 2*Qe()-s.renderingStartTime>$i&&a!==536870912&&(t.flags|=128,l=!0,Zl(s,!1),t.lanes=4194304);s.isBackwards?(c.sibling=t.child,t.child=c):(e=s.last,e!==null?e.sibling=c:t.child=c,s.last=c)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Qe(),t.sibling=null,e=Dt.current,re(Dt,l?e&1|2:e&1),t):(bt(t),null);case 22:case 23:return ra(t),Yu(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(bt(t),t.subtreeFlags&6&&(t.flags|=8192)):bt(t),a=t.updateQueue,a!==null&&Di(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&le(or),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),na(Bt),bt(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function bb(e,t){switch(ku(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return na(Bt),_e(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Me(t),null;case 13:if(ra(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));Nl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(Dt),null;case 4:return _e(),null;case 10:return na(t.type),null;case 22:case 23:return ra(t),Yu(),e!==null&&le(or),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return na(Bt),null;case 25:return null;default:return null}}function eh(e,t){switch(ku(t),t.tag){case 3:na(Bt),_e();break;case 26:case 27:case 5:Me(t);break;case 4:_e();break;case 13:ra(t);break;case 19:le(Dt);break;case 10:na(t.type);break;case 22:case 23:ra(t),Yu(),e!==null&&le(or);break;case 24:na(Bt)}}function Wl(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var s=l.next;a=s;do{if((a.tag&e)===e){l=void 0;var c=a.create,m=a.inst;l=c(),m.destroy=l}a=a.next}while(a!==s)}}catch(y){ut(t,t.return,y)}}function wa(e,t,a){try{var l=t.updateQueue,s=l!==null?l.lastEffect:null;if(s!==null){var c=s.next;l=c;do{if((l.tag&e)===e){var m=l.inst,y=m.destroy;if(y!==void 0){m.destroy=void 0,s=t;var z=a,H=y;try{H()}catch(W){ut(s,z,W)}}}l=l.next}while(l!==c)}}catch(W){ut(t,t.return,W)}}function th(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Pp(t,a)}catch(l){ut(e,e.return,l)}}}function nh(e,t,a){a.props=sr(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){ut(e,t,l)}}function Jl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(s){ut(e,t,s)}}function _n(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(s){ut(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){ut(e,t,s)}else a.current=null}function ah(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(s){ut(e,e.return,s)}}function bc(e,t,a){try{var l=e.stateNode;Hb(l,e.type,a,t),l[Wt]=t}catch(s){ut(e,e.return,s)}}function rh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&$a(e.type)||e.tag===4}function Sc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&$a(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function xc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Ii));else if(l!==4&&(l===27&&$a(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(xc(e,t,a),e=e.sibling;e!==null;)xc(e,t,a),e=e.sibling}function Ni(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&$a(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ni(e,t,a),e=e.sibling;e!==null;)Ni(e,t,a),e=e.sibling}function lh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);Vt(t,l,a),t[Yt]=e,t[Wt]=a}catch(c){ut(e,e.return,c)}}var ia=!1,Mt=!1,Cc=!1,oh=typeof WeakSet=="function"?WeakSet:Set,$t=null;function Sb(e,t){if(e=e.containerInfo,Ic=Ji,e=yp(e),Su(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var s=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var m=0,y=-1,z=-1,H=0,W=0,ae=e,q=null;t:for(;;){for(var V;ae!==a||s!==0&&ae.nodeType!==3||(y=m+s),ae!==c||l!==0&&ae.nodeType!==3||(z=m+l),ae.nodeType===3&&(m+=ae.nodeValue.length),(V=ae.firstChild)!==null;)q=ae,ae=V;for(;;){if(ae===e)break t;if(q===a&&++H===s&&(y=m),q===c&&++W===l&&(z=m),(V=ae.nextSibling)!==null)break;ae=q,q=ae.parentNode}ae=V}a=y===-1||z===-1?null:{start:y,end:z}}else a=null}a=a||{start:0,end:0}}else a=null;for(Xc={focusedElem:e,selectionRange:a},Ji=!1,$t=t;$t!==null;)if(t=$t,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,$t=e;else for(;$t!==null;){switch(t=$t,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,s=c.memoizedProps,c=c.memoizedState,l=a.stateNode;try{var be=sr(a.type,s,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(be,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(ye){ut(a,a.return,ye)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Fc(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Fc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,$t=e;break}$t=t.return}}function ih(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:za(e,a),l&4&&Wl(5,a);break;case 1:if(za(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(m){ut(a,a.return,m)}else{var s=sr(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(m){ut(a,a.return,m)}}l&64&&th(a),l&512&&Jl(a,a.return);break;case 3:if(za(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Pp(e,t)}catch(m){ut(a,a.return,m)}}break;case 27:t===null&&l&4&&lh(a);case 26:case 5:za(e,a),t===null&&l&4&&ah(a),l&512&&Jl(a,a.return);break;case 12:za(e,a);break;case 13:za(e,a),l&4&&ch(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=wb.bind(null,a),Xb(e,a))));break;case 22:if(l=a.memoizedState!==null||ia,!l){t=t!==null&&t.memoizedState!==null||Mt,s=ia;var c=Mt;ia=l,(Mt=t)&&!c?ka(e,a,(a.subtreeFlags&8772)!==0):za(e,a),ia=s,Mt=c}break;case 30:break;default:za(e,a)}}function sh(e){var t=e.alternate;t!==null&&(e.alternate=null,sh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&tu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ht=null,tn=!1;function sa(e,t,a){for(a=a.child;a!==null;)uh(e,t,a),a=a.sibling}function uh(e,t,a){if(Ee&&typeof Ee.onCommitFiberUnmount=="function")try{Ee.onCommitFiberUnmount(mt,a)}catch{}switch(a.tag){case 26:Mt||_n(a,t),sa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Mt||_n(a,t);var l=ht,s=tn;$a(a.type)&&(ht=a.stateNode,tn=!1),sa(e,t,a),so(a.stateNode),ht=l,tn=s;break;case 5:Mt||_n(a,t);case 6:if(l=ht,s=tn,ht=null,sa(e,t,a),ht=l,tn=s,ht!==null)if(tn)try{(ht.nodeType===9?ht.body:ht.nodeName==="HTML"?ht.ownerDocument.body:ht).removeChild(a.stateNode)}catch(c){ut(a,t,c)}else try{ht.removeChild(a.stateNode)}catch(c){ut(a,t,c)}break;case 18:ht!==null&&(tn?(e=ht,Zh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),yo(e)):Zh(ht,a.stateNode));break;case 4:l=ht,s=tn,ht=a.stateNode.containerInfo,tn=!0,sa(e,t,a),ht=l,tn=s;break;case 0:case 11:case 14:case 15:Mt||wa(2,a,t),Mt||wa(4,a,t),sa(e,t,a);break;case 1:Mt||(_n(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&nh(a,t,l)),sa(e,t,a);break;case 21:sa(e,t,a);break;case 22:Mt=(l=Mt)||a.memoizedState!==null,sa(e,t,a),Mt=l;break;default:sa(e,t,a)}}function ch(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{yo(e)}catch(a){ut(t,t.return,a)}}function xb(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new oh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new oh),t;default:throw Error(i(435,e.tag))}}function Tc(e,t){var a=xb(e);t.forEach(function(l){var s=zb.bind(null,e,l);a.has(l)||(a.add(l),l.then(s,s))})}function sn(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var s=a[l],c=e,m=t,y=m;e:for(;y!==null;){switch(y.tag){case 27:if($a(y.type)){ht=y.stateNode,tn=!1;break e}break;case 5:ht=y.stateNode,tn=!1;break e;case 3:case 4:ht=y.stateNode.containerInfo,tn=!0;break e}y=y.return}if(ht===null)throw Error(i(160));uh(c,m,s),ht=null,tn=!1,c=s.alternate,c!==null&&(c.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)fh(t,e),t=t.sibling}var zn=null;function fh(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:sn(t,e),un(e),l&4&&(wa(3,e,e.return),Wl(3,e),wa(5,e,e.return));break;case 1:sn(t,e),un(e),l&512&&(Mt||a===null||_n(a,a.return)),l&64&&ia&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var s=zn;if(sn(t,e),un(e),l&512&&(Mt||a===null||_n(a,a.return)),l&4){var c=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,s=s.ownerDocument||s;t:switch(l){case"title":c=s.getElementsByTagName("title")[0],(!c||c[Tl]||c[Yt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=s.createElement(l),s.head.insertBefore(c,s.querySelector("head > title"))),Vt(c,l,a),c[Yt]=e,Nt(c),l=c;break e;case"link":var m=lg("link","href",s).get(l+(a.href||""));if(m){for(var y=0;y<m.length;y++)if(c=m[y],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){m.splice(y,1);break t}}c=s.createElement(l),Vt(c,l,a),s.head.appendChild(c);break;case"meta":if(m=lg("meta","content",s).get(l+(a.content||""))){for(y=0;y<m.length;y++)if(c=m[y],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){m.splice(y,1);break t}}c=s.createElement(l),Vt(c,l,a),s.head.appendChild(c);break;default:throw Error(i(468,l))}c[Yt]=e,Nt(c),l=c}e.stateNode=l}else og(s,e.type,e.stateNode);else e.stateNode=rg(s,l,e.memoizedProps);else c!==l?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,l===null?og(s,e.type,e.stateNode):rg(s,l,e.memoizedProps)):l===null&&e.stateNode!==null&&bc(e,e.memoizedProps,a.memoizedProps)}break;case 27:sn(t,e),un(e),l&512&&(Mt||a===null||_n(a,a.return)),a!==null&&l&4&&bc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(sn(t,e),un(e),l&512&&(Mt||a===null||_n(a,a.return)),e.flags&32){s=e.stateNode;try{zr(s,"")}catch(V){ut(e,e.return,V)}}l&4&&e.stateNode!=null&&(s=e.memoizedProps,bc(e,s,a!==null?a.memoizedProps:s)),l&1024&&(Cc=!0);break;case 6:if(sn(t,e),un(e),l&4){if(e.stateNode===null)throw Error(i(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(V){ut(e,e.return,V)}}break;case 3:if(Fi=null,s=zn,zn=Ki(t.containerInfo),sn(t,e),zn=s,un(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{yo(t.containerInfo)}catch(V){ut(e,e.return,V)}Cc&&(Cc=!1,dh(e));break;case 4:l=zn,zn=Ki(e.stateNode.containerInfo),sn(t,e),un(e),zn=l;break;case 12:sn(t,e),un(e);break;case 13:sn(t,e),un(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(wc=Qe()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Tc(e,l)));break;case 22:s=e.memoizedState!==null;var z=a!==null&&a.memoizedState!==null,H=ia,W=Mt;if(ia=H||s,Mt=W||z,sn(t,e),Mt=W,ia=H,un(e),l&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(a===null||z||ia||Mt||ur(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){z=a=t;try{if(c=z.stateNode,s)m=c.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{y=z.stateNode;var ae=z.memoizedProps.style,q=ae!=null&&ae.hasOwnProperty("display")?ae.display:null;y.style.display=q==null||typeof q=="boolean"?"":(""+q).trim()}}catch(V){ut(z,z.return,V)}}}else if(t.tag===6){if(a===null){z=t;try{z.stateNode.nodeValue=s?"":z.memoizedProps}catch(V){ut(z,z.return,V)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Tc(e,a))));break;case 19:sn(t,e),un(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Tc(e,l)));break;case 30:break;case 21:break;default:sn(t,e),un(e)}}function un(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(rh(l)){a=l;break}l=l.return}if(a==null)throw Error(i(160));switch(a.tag){case 27:var s=a.stateNode,c=Sc(e);Ni(e,c,s);break;case 5:var m=a.stateNode;a.flags&32&&(zr(m,""),a.flags&=-33);var y=Sc(e);Ni(e,y,m);break;case 3:case 4:var z=a.stateNode.containerInfo,H=Sc(e);xc(e,H,z);break;default:throw Error(i(161))}}catch(W){ut(e,e.return,W)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function dh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;dh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function za(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)ih(e,t.alternate,t),t=t.sibling}function ur(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:wa(4,t,t.return),ur(t);break;case 1:_n(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&nh(t,t.return,a),ur(t);break;case 27:so(t.stateNode);case 26:case 5:_n(t,t.return),ur(t);break;case 22:t.memoizedState===null&&ur(t);break;case 30:ur(t);break;default:ur(t)}e=e.sibling}}function ka(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,s=e,c=t,m=c.flags;switch(c.tag){case 0:case 11:case 15:ka(s,c,a),Wl(4,c);break;case 1:if(ka(s,c,a),l=c,s=l.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(H){ut(l,l.return,H)}if(l=c,s=l.updateQueue,s!==null){var y=l.stateNode;try{var z=s.shared.hiddenCallbacks;if(z!==null)for(s.shared.hiddenCallbacks=null,s=0;s<z.length;s++)qp(z[s],y)}catch(H){ut(l,l.return,H)}}a&&m&64&&th(c),Jl(c,c.return);break;case 27:lh(c);case 26:case 5:ka(s,c,a),a&&l===null&&m&4&&ah(c),Jl(c,c.return);break;case 12:ka(s,c,a);break;case 13:ka(s,c,a),a&&m&4&&ch(s,c);break;case 22:c.memoizedState===null&&ka(s,c,a),Jl(c,c.return);break;case 30:break;default:ka(s,c,a)}t=t.sibling}}function Ec(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Ll(a))}function Rc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ll(e))}function $n(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ph(e,t,a,l),t=t.sibling}function ph(e,t,a,l){var s=t.flags;switch(t.tag){case 0:case 11:case 15:$n(e,t,a,l),s&2048&&Wl(9,t);break;case 1:$n(e,t,a,l);break;case 3:$n(e,t,a,l),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ll(e)));break;case 12:if(s&2048){$n(e,t,a,l),e=t.stateNode;try{var c=t.memoizedProps,m=c.id,y=c.onPostCommit;typeof y=="function"&&y(m,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(z){ut(t,t.return,z)}}else $n(e,t,a,l);break;case 13:$n(e,t,a,l);break;case 23:break;case 22:c=t.stateNode,m=t.alternate,t.memoizedState!==null?c._visibility&2?$n(e,t,a,l):eo(e,t):c._visibility&2?$n(e,t,a,l):(c._visibility|=2,Qr(e,t,a,l,(t.subtreeFlags&10256)!==0)),s&2048&&Ec(m,t);break;case 24:$n(e,t,a,l),s&2048&&Rc(t.alternate,t);break;default:$n(e,t,a,l)}}function Qr(e,t,a,l,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,m=t,y=a,z=l,H=m.flags;switch(m.tag){case 0:case 11:case 15:Qr(c,m,y,z,s),Wl(8,m);break;case 23:break;case 22:var W=m.stateNode;m.memoizedState!==null?W._visibility&2?Qr(c,m,y,z,s):eo(c,m):(W._visibility|=2,Qr(c,m,y,z,s)),s&&H&2048&&Ec(m.alternate,m);break;case 24:Qr(c,m,y,z,s),s&&H&2048&&Rc(m.alternate,m);break;default:Qr(c,m,y,z,s)}t=t.sibling}}function eo(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,s=l.flags;switch(l.tag){case 22:eo(a,l),s&2048&&Ec(l.alternate,l);break;case 24:eo(a,l),s&2048&&Rc(l.alternate,l);break;default:eo(a,l)}t=t.sibling}}var to=8192;function Fr(e){if(e.subtreeFlags&to)for(e=e.child;e!==null;)mh(e),e=e.sibling}function mh(e){switch(e.tag){case 26:Fr(e),e.flags&to&&e.memoizedState!==null&&o1(zn,e.memoizedState,e.memoizedProps);break;case 5:Fr(e);break;case 3:case 4:var t=zn;zn=Ki(e.stateNode.containerInfo),Fr(e),zn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=to,to=16777216,Fr(e),to=t):Fr(e));break;default:Fr(e)}}function hh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function no(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];$t=l,yh(l,e)}hh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)gh(e),e=e.sibling}function gh(e){switch(e.tag){case 0:case 11:case 15:no(e),e.flags&2048&&wa(9,e,e.return);break;case 3:no(e);break;case 12:no(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,_i(e)):no(e);break;default:no(e)}}function _i(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];$t=l,yh(l,e)}hh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:wa(8,t,t.return),_i(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,_i(t));break;default:_i(t)}e=e.sibling}}function yh(e,t){for(;$t!==null;){var a=$t;switch(a.tag){case 0:case 11:case 15:wa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ll(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,$t=l;else e:for(a=e;$t!==null;){l=$t;var s=l.sibling,c=l.return;if(sh(l),l===a){$t=null;break e}if(s!==null){s.return=c,$t=s;break e}$t=c}}}var Cb={getCacheForType:function(e){var t=It(Bt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Tb=typeof WeakMap=="function"?WeakMap:Map,Fe=0,ct=null,ke=null,Le=0,Ze=0,cn=null,ja=!1,Zr=!1,Ac=!1,ua=0,Tt=0,Ba=0,cr=0,Mc=0,Tn=0,Wr=0,ao=null,nn=null,Oc=!1,wc=0,$i=1/0,Li=null,Da=null,Pt=0,Na=null,Jr=null,el=0,zc=0,kc=null,vh=null,ro=0,jc=null;function fn(){if((Fe&2)!==0&&Le!==0)return Le&-Le;if(w.T!==null){var e=qr;return e!==0?e:Uc()}return Dd()}function bh(){Tn===0&&(Tn=(Le&536870912)===0||Pe?xl():536870912);var e=Cn.current;return e!==null&&(e.flags|=32),Tn}function dn(e,t,a){(e===ct&&(Ze===2||Ze===9)||e.cancelPendingCommit!==null)&&(tl(e,0),_a(e,Le,Tn,!1)),Cl(e,a),((Fe&2)===0||e!==ct)&&(e===ct&&((Fe&2)===0&&(cr|=a),Tt===4&&_a(e,Le,Tn,!1)),Ln(e))}function Sh(e,t,a){if((Fe&6)!==0)throw Error(i(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||vt(e,t),s=l?Ab(e,t):Nc(e,t,!0),c=l;do{if(s===0){Zr&&!l&&_a(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!Eb(a)){s=Nc(e,t,!1),c=!1;continue}if(s===2){if(c=t,e.errorRecoveryDisabledLanes&c)var m=0;else m=e.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){t=m;e:{var y=e;s=ao;var z=y.current.memoizedState.isDehydrated;if(z&&(tl(y,m).flags|=256),m=Nc(y,m,!1),m!==2){if(Ac&&!z){y.errorRecoveryDisabledLanes|=c,cr|=c,s=4;break e}c=nn,nn=s,c!==null&&(nn===null?nn=c:nn.push.apply(nn,c))}s=m}if(c=!1,s!==2)continue}}if(s===1){tl(e,0),_a(e,t,0,!0);break}e:{switch(l=e,c=s,c){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:_a(l,t,Tn,!ja);break e;case 2:nn=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(s=wc+300-Qe(),10<s)){if(_a(l,t,Tn,!ja),Ie(l,0,!0)!==0)break e;l.timeoutHandle=Qh(xh.bind(null,l,a,nn,Li,Oc,t,Tn,cr,Wr,ja,c,2,-0,0),s);break e}xh(l,a,nn,Li,Oc,t,Tn,cr,Wr,ja,c,0,-0,0)}}break}while(!0);Ln(e)}function xh(e,t,a,l,s,c,m,y,z,H,W,ae,q,V){if(e.timeoutHandle=-1,ae=t.subtreeFlags,(ae&8192||(ae&16785408)===16785408)&&(fo={stylesheets:null,count:0,unsuspend:l1},mh(t),ae=i1(),ae!==null)){e.cancelPendingCommit=ae(Oh.bind(null,e,t,c,a,l,s,m,y,z,W,1,q,V)),_a(e,c,m,!H);return}Oh(e,t,c,a,l,s,m,y,z)}function Eb(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var s=a[l],c=s.getSnapshot;s=s.value;try{if(!ln(c(),s))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function _a(e,t,a,l){t&=~Mc,t&=~cr,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var s=t;0<s;){var c=31-qe(s),m=1<<c;l[c]=-1,s&=~m}a!==0&&jd(e,a,t)}function Ui(){return(Fe&6)===0?(lo(0),!1):!0}function Bc(){if(ke!==null){if(Ze===0)var e=ke.return;else e=ke,ta=rr=null,Fu(e),Xr=null,Ql=0,e=ke;for(;e!==null;)eh(e.alternate,e),e=e.return;ke=null}}function tl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Pb(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Bc(),ct=e,ke=a=Wn(e.current,null),Le=t,Ze=0,cn=null,ja=!1,Zr=vt(e,t),Ac=!1,Wr=Tn=Mc=cr=Ba=Tt=0,nn=ao=null,Oc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var s=31-qe(l),c=1<<s;t|=e[s],l&=~c}return ua=t,ii(),a}function Ch(e,t){Ae=null,w.H=Ai,t===Hl||t===gi?(t=Up(),Ze=3):t===_p?(t=Up(),Ze=4):Ze=t===Hm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,cn=t,ke===null&&(Tt=1,ki(e,vn(t,e.current)))}function Th(){var e=w.H;return w.H=Ai,e===null?Ai:e}function Eh(){var e=w.A;return w.A=Cb,e}function Dc(){Tt=4,ja||(Le&4194048)!==Le&&Cn.current!==null||(Zr=!0),(Ba&134217727)===0&&(cr&134217727)===0||ct===null||_a(ct,Le,Tn,!1)}function Nc(e,t,a){var l=Fe;Fe|=2;var s=Th(),c=Eh();(ct!==e||Le!==t)&&(Li=null,tl(e,t)),t=!1;var m=Tt;e:do try{if(Ze!==0&&ke!==null){var y=ke,z=cn;switch(Ze){case 8:Bc(),m=6;break e;case 3:case 2:case 9:case 6:Cn.current===null&&(t=!0);var H=Ze;if(Ze=0,cn=null,nl(e,y,z,H),a&&Zr){m=0;break e}break;default:H=Ze,Ze=0,cn=null,nl(e,y,z,H)}}Rb(),m=Tt;break}catch(W){Ch(e,W)}while(!0);return t&&e.shellSuspendCounter++,ta=rr=null,Fe=l,w.H=s,w.A=c,ke===null&&(ct=null,Le=0,ii()),m}function Rb(){for(;ke!==null;)Rh(ke)}function Ab(e,t){var a=Fe;Fe|=2;var l=Th(),s=Eh();ct!==e||Le!==t?(Li=null,$i=Qe()+500,tl(e,t)):Zr=vt(e,t);e:do try{if(Ze!==0&&ke!==null){t=ke;var c=cn;t:switch(Ze){case 1:Ze=0,cn=null,nl(e,t,c,1);break;case 2:case 9:if($p(c)){Ze=0,cn=null,Ah(t);break}t=function(){Ze!==2&&Ze!==9||ct!==e||(Ze=7),Ln(e)},c.then(t,t);break e;case 3:Ze=7;break e;case 4:Ze=5;break e;case 7:$p(c)?(Ze=0,cn=null,Ah(t)):(Ze=0,cn=null,nl(e,t,c,7));break;case 5:var m=null;switch(ke.tag){case 26:m=ke.memoizedState;case 5:case 27:var y=ke;if(!m||ig(m)){Ze=0,cn=null;var z=y.sibling;if(z!==null)ke=z;else{var H=y.return;H!==null?(ke=H,Hi(H)):ke=null}break t}}Ze=0,cn=null,nl(e,t,c,5);break;case 6:Ze=0,cn=null,nl(e,t,c,6);break;case 8:Bc(),Tt=6;break e;default:throw Error(i(462))}}Mb();break}catch(W){Ch(e,W)}while(!0);return ta=rr=null,w.H=l,w.A=s,Fe=a,ke!==null?0:(ct=null,Le=0,ii(),Tt)}function Mb(){for(;ke!==null&&!Ke();)Rh(ke)}function Rh(e){var t=Wm(e.alternate,e,ua);e.memoizedProps=e.pendingProps,t===null?Hi(e):ke=t}function Ah(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Im(a,t,t.pendingProps,t.type,void 0,Le);break;case 11:t=Im(a,t,t.pendingProps,t.type.render,t.ref,Le);break;case 5:Fu(t);default:eh(a,t),t=ke=Mp(t,ua),t=Wm(a,t,ua)}e.memoizedProps=e.pendingProps,t===null?Hi(e):ke=t}function nl(e,t,a,l){ta=rr=null,Fu(t),Xr=null,Ql=0;var s=t.return;try{if(gb(e,s,t,a,Le)){Tt=1,ki(e,vn(a,e.current)),ke=null;return}}catch(c){if(s!==null)throw ke=s,c;Tt=1,ki(e,vn(a,e.current)),ke=null;return}t.flags&32768?(Pe||l===1?e=!0:Zr||(Le&536870912)!==0?e=!1:(ja=e=!0,(l===2||l===9||l===3||l===6)&&(l=Cn.current,l!==null&&l.tag===13&&(l.flags|=16384))),Mh(t,e)):Hi(t)}function Hi(e){var t=e;do{if((t.flags&32768)!==0){Mh(t,ja);return}e=t.return;var a=vb(t.alternate,t,ua);if(a!==null){ke=a;return}if(t=t.sibling,t!==null){ke=t;return}ke=t=e}while(t!==null);Tt===0&&(Tt=5)}function Mh(e,t){do{var a=bb(e.alternate,e);if(a!==null){a.flags&=32767,ke=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ke=e;return}ke=e=a}while(e!==null);Tt=6,ke=null}function Oh(e,t,a,l,s,c,m,y,z){e.cancelPendingCommit=null;do qi();while(Pt!==0);if((Fe&6)!==0)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(c=t.lanes|t.childLanes,c|=Ru,lv(e,a,c,m,y,z),e===ct&&(ke=ct=null,Le=0),Jr=t,Na=e,el=a,zc=c,kc=s,vh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,kb(tt,function(){return Bh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=w.T,w.T=null,s=K.p,K.p=2,m=Fe,Fe|=4;try{Sb(e,t,a)}finally{Fe=m,K.p=s,w.T=l}}Pt=1,wh(),zh(),kh()}}function wh(){if(Pt===1){Pt=0;var e=Na,t=Jr,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=w.T,w.T=null;var l=K.p;K.p=2;var s=Fe;Fe|=4;try{fh(t,e);var c=Xc,m=yp(e.containerInfo),y=c.focusedElem,z=c.selectionRange;if(m!==y&&y&&y.ownerDocument&&gp(y.ownerDocument.documentElement,y)){if(z!==null&&Su(y)){var H=z.start,W=z.end;if(W===void 0&&(W=H),"selectionStart"in y)y.selectionStart=H,y.selectionEnd=Math.min(W,y.value.length);else{var ae=y.ownerDocument||document,q=ae&&ae.defaultView||window;if(q.getSelection){var V=q.getSelection(),be=y.textContent.length,ye=Math.min(z.start,be),ot=z.end===void 0?ye:Math.min(z.end,be);!V.extend&&ye>ot&&(m=ot,ot=ye,ye=m);var $=hp(y,ye),D=hp(y,ot);if($&&D&&(V.rangeCount!==1||V.anchorNode!==$.node||V.anchorOffset!==$.offset||V.focusNode!==D.node||V.focusOffset!==D.offset)){var L=ae.createRange();L.setStart($.node,$.offset),V.removeAllRanges(),ye>ot?(V.addRange(L),V.extend(D.node,D.offset)):(L.setEnd(D.node,D.offset),V.addRange(L))}}}}for(ae=[],V=y;V=V.parentNode;)V.nodeType===1&&ae.push({element:V,left:V.scrollLeft,top:V.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<ae.length;y++){var ee=ae[y];ee.element.scrollLeft=ee.left,ee.element.scrollTop=ee.top}}Ji=!!Ic,Xc=Ic=null}finally{Fe=s,K.p=l,w.T=a}}e.current=t,Pt=2}}function zh(){if(Pt===2){Pt=0;var e=Na,t=Jr,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=w.T,w.T=null;var l=K.p;K.p=2;var s=Fe;Fe|=4;try{ih(e,t.alternate,t)}finally{Fe=s,K.p=l,w.T=a}}Pt=3}}function kh(){if(Pt===4||Pt===3){Pt=0,Ht();var e=Na,t=Jr,a=el,l=vh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Pt=5:(Pt=0,Jr=Na=null,jh(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Da=null),Js(a),t=t.stateNode,Ee&&typeof Ee.onCommitFiberRoot=="function")try{Ee.onCommitFiberRoot(mt,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=w.T,s=K.p,K.p=2,w.T=null;try{for(var c=e.onRecoverableError,m=0;m<l.length;m++){var y=l[m];c(y.value,{componentStack:y.stack})}}finally{w.T=t,K.p=s}}(el&3)!==0&&qi(),Ln(e),s=e.pendingLanes,(a&4194090)!==0&&(s&42)!==0?e===jc?ro++:(ro=0,jc=e):ro=0,lo(0)}}function jh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ll(t)))}function qi(e){return wh(),zh(),kh(),Bh()}function Bh(){if(Pt!==5)return!1;var e=Na,t=zc;zc=0;var a=Js(el),l=w.T,s=K.p;try{K.p=32>a?32:a,w.T=null,a=kc,kc=null;var c=Na,m=el;if(Pt=0,Jr=Na=null,el=0,(Fe&6)!==0)throw Error(i(331));var y=Fe;if(Fe|=4,gh(c.current),ph(c,c.current,m,a),Fe=y,lo(0,!1),Ee&&typeof Ee.onPostCommitFiberRoot=="function")try{Ee.onPostCommitFiberRoot(mt,c)}catch{}return!0}finally{K.p=s,w.T=l,jh(e,t)}}function Dh(e,t,a){t=vn(a,t),t=cc(e.stateNode,t,2),e=Ra(e,t,2),e!==null&&(Cl(e,2),Ln(e))}function ut(e,t,a){if(e.tag===3)Dh(e,e,a);else for(;t!==null;){if(t.tag===3){Dh(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Da===null||!Da.has(l))){e=vn(a,e),a=Lm(2),l=Ra(t,a,2),l!==null&&(Um(a,l,t,e),Cl(l,2),Ln(l));break}}t=t.return}}function _c(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Tb;var s=new Set;l.set(t,s)}else s=l.get(t),s===void 0&&(s=new Set,l.set(t,s));s.has(a)||(Ac=!0,s.add(a),e=Ob.bind(null,e,t,a),t.then(e,e))}function Ob(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,ct===e&&(Le&a)===a&&(Tt===4||Tt===3&&(Le&62914560)===Le&&300>Qe()-wc?(Fe&2)===0&&tl(e,0):Mc|=a,Wr===Le&&(Wr=0)),Ln(e)}function Nh(e,t){t===0&&(t=kd()),e=$r(e,t),e!==null&&(Cl(e,t),Ln(e))}function wb(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Nh(e,a)}function zb(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,s=e.memoizedState;s!==null&&(a=s.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(i(314))}l!==null&&l.delete(t),Nh(e,a)}function kb(e,t){return St(e,t)}var Pi=null,al=null,$c=!1,Vi=!1,Lc=!1,fr=0;function Ln(e){e!==al&&e.next===null&&(al===null?Pi=al=e:al=al.next=e),Vi=!0,$c||($c=!0,Bb())}function lo(e,t){if(!Lc&&Vi){Lc=!0;do for(var a=!1,l=Pi;l!==null;){if(e!==0){var s=l.pendingLanes;if(s===0)var c=0;else{var m=l.suspendedLanes,y=l.pingedLanes;c=(1<<31-qe(42|e)+1)-1,c&=s&~(m&~y),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Uh(l,c))}else c=Le,c=Ie(l,l===ct?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||vt(l,c)||(a=!0,Uh(l,c));l=l.next}while(a);Lc=!1}}function jb(){_h()}function _h(){Vi=$c=!1;var e=0;fr!==0&&(qb()&&(e=fr),fr=0);for(var t=Qe(),a=null,l=Pi;l!==null;){var s=l.next,c=$h(l,t);c===0?(l.next=null,a===null?Pi=s:a.next=s,s===null&&(al=a)):(a=l,(e!==0||(c&3)!==0)&&(Vi=!0)),l=s}lo(e)}function $h(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,s=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var m=31-qe(c),y=1<<m,z=s[m];z===-1?((y&a)===0||(y&l)!==0)&&(s[m]=ba(y,t)):z<=t&&(e.expiredLanes|=y),c&=~y}if(t=ct,a=Le,a=Ie(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Ze===2||Ze===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Te(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||vt(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Te(l),Js(a)){case 2:case 8:a=yt;break;case 32:a=tt;break;case 268435456:a=rn;break;default:a=tt}return l=Lh.bind(null,e),a=St(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Te(l),e.callbackPriority=2,e.callbackNode=null,2}function Lh(e,t){if(Pt!==0&&Pt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(qi()&&e.callbackNode!==a)return null;var l=Le;return l=Ie(e,e===ct?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Sh(e,l,t),$h(e,Qe()),e.callbackNode!=null&&e.callbackNode===a?Lh.bind(null,e):null)}function Uh(e,t){if(qi())return null;Sh(e,t,!0)}function Bb(){Vb(function(){(Fe&6)!==0?St(dt,jb):_h()})}function Uc(){return fr===0&&(fr=xl()),fr}function Hh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ei(""+e)}function qh(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Db(e,t,a,l,s){if(t==="submit"&&a&&a.stateNode===s){var c=Hh((s[Wt]||null).action),m=l.submitter;m&&(t=(t=m[Wt]||null)?Hh(t.formAction):m.getAttribute("formAction"),t!==null&&(c=t,m=null));var y=new ri("action","action",null,l,s);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(fr!==0){var z=m?qh(s,m):new FormData(s);lc(a,{pending:!0,data:z,method:s.method,action:c},null,z)}}else typeof c=="function"&&(y.preventDefault(),z=m?qh(s,m):new FormData(s),lc(a,{pending:!0,data:z,method:s.method,action:c},c,z))},currentTarget:s}]})}}for(var Hc=0;Hc<Eu.length;Hc++){var qc=Eu[Hc],Nb=qc.toLowerCase(),_b=qc[0].toUpperCase()+qc.slice(1);wn(Nb,"on"+_b)}wn(Sp,"onAnimationEnd"),wn(xp,"onAnimationIteration"),wn(Cp,"onAnimationStart"),wn("dblclick","onDoubleClick"),wn("focusin","onFocus"),wn("focusout","onBlur"),wn(Jv,"onTransitionRun"),wn(eb,"onTransitionStart"),wn(tb,"onTransitionCancel"),wn(Tp,"onTransitionEnd"),Mr("onMouseEnter",["mouseout","mouseover"]),Mr("onMouseLeave",["mouseout","mouseover"]),Mr("onPointerEnter",["pointerout","pointerover"]),Mr("onPointerLeave",["pointerout","pointerover"]),Qa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qa("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var oo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),$b=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(oo));function Ph(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],s=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var m=l.length-1;0<=m;m--){var y=l[m],z=y.instance,H=y.currentTarget;if(y=y.listener,z!==c&&s.isPropagationStopped())break e;c=y,s.currentTarget=H;try{c(s)}catch(W){zi(W)}s.currentTarget=null,c=z}else for(m=0;m<l.length;m++){if(y=l[m],z=y.instance,H=y.currentTarget,y=y.listener,z!==c&&s.isPropagationStopped())break e;c=y,s.currentTarget=H;try{c(s)}catch(W){zi(W)}s.currentTarget=null,c=z}}}}function je(e,t){var a=t[eu];a===void 0&&(a=t[eu]=new Set);var l=e+"__bubble";a.has(l)||(Vh(t,e,2,!1),a.add(l))}function Pc(e,t,a){var l=0;t&&(l|=4),Vh(a,e,l,t)}var Gi="_reactListening"+Math.random().toString(36).slice(2);function Vc(e){if(!e[Gi]){e[Gi]=!0,_d.forEach(function(a){a!=="selectionchange"&&($b.has(a)||Pc(a,!1,e),Pc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Gi]||(t[Gi]=!0,Pc("selectionchange",!1,t))}}function Vh(e,t,a,l){switch(pg(t)){case 2:var s=c1;break;case 8:s=f1;break;default:s=af}a=s.bind(null,t,a,e),s=void 0,!fu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),l?s!==void 0?e.addEventListener(t,a,{capture:!0,passive:s}):e.addEventListener(t,a,!0):s!==void 0?e.addEventListener(t,a,{passive:s}):e.addEventListener(t,a,!1)}function Gc(e,t,a,l,s){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var m=l.tag;if(m===3||m===4){var y=l.stateNode.containerInfo;if(y===s)break;if(m===4)for(m=l.return;m!==null;){var z=m.tag;if((z===3||z===4)&&m.stateNode.containerInfo===s)return;m=m.return}for(;y!==null;){if(m=Er(y),m===null)return;if(z=m.tag,z===5||z===6||z===26||z===27){l=c=m;continue e}y=y.parentNode}}l=l.return}Fd(function(){var H=c,W=uu(a),ae=[];e:{var q=Ep.get(e);if(q!==void 0){var V=ri,be=e;switch(e){case"keypress":if(ni(a)===0)break e;case"keydown":case"keyup":V=kv;break;case"focusin":be="focus",V=hu;break;case"focusout":be="blur",V=hu;break;case"beforeblur":case"afterblur":V=hu;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":V=Jd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":V=bv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":V=Dv;break;case Sp:case xp:case Cp:V=Cv;break;case Tp:V=_v;break;case"scroll":case"scrollend":V=yv;break;case"wheel":V=Lv;break;case"copy":case"cut":case"paste":V=Ev;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":V=tp;break;case"toggle":case"beforetoggle":V=Hv}var ye=(t&4)!==0,ot=!ye&&(e==="scroll"||e==="scrollend"),$=ye?q!==null?q+"Capture":null:q;ye=[];for(var D=H,L;D!==null;){var ee=D;if(L=ee.stateNode,ee=ee.tag,ee!==5&&ee!==26&&ee!==27||L===null||$===null||(ee=Rl(D,$),ee!=null&&ye.push(io(D,ee,L))),ot)break;D=D.return}0<ye.length&&(q=new V(q,be,null,a,W),ae.push({event:q,listeners:ye}))}}if((t&7)===0){e:{if(q=e==="mouseover"||e==="pointerover",V=e==="mouseout"||e==="pointerout",q&&a!==su&&(be=a.relatedTarget||a.fromElement)&&(Er(be)||be[Tr]))break e;if((V||q)&&(q=W.window===W?W:(q=W.ownerDocument)?q.defaultView||q.parentWindow:window,V?(be=a.relatedTarget||a.toElement,V=H,be=be?Er(be):null,be!==null&&(ot=f(be),ye=be.tag,be!==ot||ye!==5&&ye!==27&&ye!==6)&&(be=null)):(V=null,be=H),V!==be)){if(ye=Jd,ee="onMouseLeave",$="onMouseEnter",D="mouse",(e==="pointerout"||e==="pointerover")&&(ye=tp,ee="onPointerLeave",$="onPointerEnter",D="pointer"),ot=V==null?q:El(V),L=be==null?q:El(be),q=new ye(ee,D+"leave",V,a,W),q.target=ot,q.relatedTarget=L,ee=null,Er(W)===H&&(ye=new ye($,D+"enter",be,a,W),ye.target=L,ye.relatedTarget=ot,ee=ye),ot=ee,V&&be)t:{for(ye=V,$=be,D=0,L=ye;L;L=rl(L))D++;for(L=0,ee=$;ee;ee=rl(ee))L++;for(;0<D-L;)ye=rl(ye),D--;for(;0<L-D;)$=rl($),L--;for(;D--;){if(ye===$||$!==null&&ye===$.alternate)break t;ye=rl(ye),$=rl($)}ye=null}else ye=null;V!==null&&Gh(ae,q,V,ye,!1),be!==null&&ot!==null&&Gh(ae,ot,be,ye,!0)}}e:{if(q=H?El(H):window,V=q.nodeName&&q.nodeName.toLowerCase(),V==="select"||V==="input"&&q.type==="file")var de=up;else if(ip(q))if(cp)de=Fv;else{de=Kv;var Oe=Xv}else V=q.nodeName,!V||V.toLowerCase()!=="input"||q.type!=="checkbox"&&q.type!=="radio"?H&&iu(H.elementType)&&(de=up):de=Qv;if(de&&(de=de(e,H))){sp(ae,de,a,W);break e}Oe&&Oe(e,q,H),e==="focusout"&&H&&q.type==="number"&&H.memoizedProps.value!=null&&ou(q,"number",q.value)}switch(Oe=H?El(H):window,e){case"focusin":(ip(Oe)||Oe.contentEditable==="true")&&(Dr=Oe,xu=H,Bl=null);break;case"focusout":Bl=xu=Dr=null;break;case"mousedown":Cu=!0;break;case"contextmenu":case"mouseup":case"dragend":Cu=!1,vp(ae,a,W);break;case"selectionchange":if(Wv)break;case"keydown":case"keyup":vp(ae,a,W)}var me;if(yu)e:{switch(e){case"compositionstart":var ve="onCompositionStart";break e;case"compositionend":ve="onCompositionEnd";break e;case"compositionupdate":ve="onCompositionUpdate";break e}ve=void 0}else Br?lp(e,a)&&(ve="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ve="onCompositionStart");ve&&(np&&a.locale!=="ko"&&(Br||ve!=="onCompositionStart"?ve==="onCompositionEnd"&&Br&&(me=Zd()):(xa=W,du="value"in xa?xa.value:xa.textContent,Br=!0)),Oe=Yi(H,ve),0<Oe.length&&(ve=new ep(ve,e,null,a,W),ae.push({event:ve,listeners:Oe}),me?ve.data=me:(me=op(a),me!==null&&(ve.data=me)))),(me=Pv?Vv(e,a):Gv(e,a))&&(ve=Yi(H,"onBeforeInput"),0<ve.length&&(Oe=new ep("onBeforeInput","beforeinput",null,a,W),ae.push({event:Oe,listeners:ve}),Oe.data=me)),Db(ae,e,H,a,W)}Ph(ae,t)})}function io(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Yi(e,t){for(var a=t+"Capture",l=[];e!==null;){var s=e,c=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||c===null||(s=Rl(e,a),s!=null&&l.unshift(io(e,s,c)),s=Rl(e,t),s!=null&&l.push(io(e,s,c))),e.tag===3)return l;e=e.return}return[]}function rl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Gh(e,t,a,l,s){for(var c=t._reactName,m=[];a!==null&&a!==l;){var y=a,z=y.alternate,H=y.stateNode;if(y=y.tag,z!==null&&z===l)break;y!==5&&y!==26&&y!==27||H===null||(z=H,s?(H=Rl(a,c),H!=null&&m.unshift(io(a,H,z))):s||(H=Rl(a,c),H!=null&&m.push(io(a,H,z)))),a=a.return}m.length!==0&&e.push({event:t,listeners:m})}var Lb=/\r\n?/g,Ub=/\u0000|\uFFFD/g;function Yh(e){return(typeof e=="string"?e:""+e).replace(Lb,`
`).replace(Ub,"")}function Ih(e,t){return t=Yh(t),Yh(e)===t}function Ii(){}function lt(e,t,a,l,s,c){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||zr(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&zr(e,""+l);break;case"className":Zo(e,"class",l);break;case"tabIndex":Zo(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Zo(e,a,l);break;case"style":Kd(e,l,c);break;case"data":if(t!=="object"){Zo(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=ei(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&lt(e,t,"name",s.name,s,null),lt(e,t,"formEncType",s.formEncType,s,null),lt(e,t,"formMethod",s.formMethod,s,null),lt(e,t,"formTarget",s.formTarget,s,null)):(lt(e,t,"encType",s.encType,s,null),lt(e,t,"method",s.method,s,null),lt(e,t,"target",s.target,s,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=ei(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=Ii);break;case"onScroll":l!=null&&je("scroll",e);break;case"onScrollEnd":l!=null&&je("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(i(61));if(a=l.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=ei(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":je("beforetoggle",e),je("toggle",e),Fo(e,"popover",l);break;case"xlinkActuate":Fn(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Fn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Fn(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Fn(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Fn(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Fn(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Fn(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Fo(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=hv.get(a)||a,Fo(e,a,l))}}function Yc(e,t,a,l,s,c){switch(a){case"style":Kd(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(i(61));if(a=l.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"children":typeof l=="string"?zr(e,l):(typeof l=="number"||typeof l=="bigint")&&zr(e,""+l);break;case"onScroll":l!=null&&je("scroll",e);break;case"onScrollEnd":l!=null&&je("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Ii);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!$d.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),t=a.slice(2,s?a.length-7:void 0),c=e[Wt]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,s),typeof l=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,s);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):Fo(e,a,l)}}}function Vt(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":je("error",e),je("load",e);var l=!1,s=!1,c;for(c in a)if(a.hasOwnProperty(c)){var m=a[c];if(m!=null)switch(c){case"src":l=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:lt(e,t,c,m,a,null)}}s&&lt(e,t,"srcSet",a.srcSet,a,null),l&&lt(e,t,"src",a.src,a,null);return;case"input":je("invalid",e);var y=c=m=s=null,z=null,H=null;for(l in a)if(a.hasOwnProperty(l)){var W=a[l];if(W!=null)switch(l){case"name":s=W;break;case"type":m=W;break;case"checked":z=W;break;case"defaultChecked":H=W;break;case"value":c=W;break;case"defaultValue":y=W;break;case"children":case"dangerouslySetInnerHTML":if(W!=null)throw Error(i(137,t));break;default:lt(e,t,l,W,a,null)}}Gd(e,c,y,z,H,m,s,!1),Wo(e);return;case"select":je("invalid",e),l=m=c=null;for(s in a)if(a.hasOwnProperty(s)&&(y=a[s],y!=null))switch(s){case"value":c=y;break;case"defaultValue":m=y;break;case"multiple":l=y;default:lt(e,t,s,y,a,null)}t=c,a=m,e.multiple=!!l,t!=null?wr(e,!!l,t,!1):a!=null&&wr(e,!!l,a,!0);return;case"textarea":je("invalid",e),c=s=l=null;for(m in a)if(a.hasOwnProperty(m)&&(y=a[m],y!=null))switch(m){case"value":l=y;break;case"defaultValue":s=y;break;case"children":c=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(i(91));break;default:lt(e,t,m,y,a,null)}Id(e,l,s,c),Wo(e);return;case"option":for(z in a)if(a.hasOwnProperty(z)&&(l=a[z],l!=null))switch(z){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:lt(e,t,z,l,a,null)}return;case"dialog":je("beforetoggle",e),je("toggle",e),je("cancel",e),je("close",e);break;case"iframe":case"object":je("load",e);break;case"video":case"audio":for(l=0;l<oo.length;l++)je(oo[l],e);break;case"image":je("error",e),je("load",e);break;case"details":je("toggle",e);break;case"embed":case"source":case"link":je("error",e),je("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(H in a)if(a.hasOwnProperty(H)&&(l=a[H],l!=null))switch(H){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:lt(e,t,H,l,a,null)}return;default:if(iu(t)){for(W in a)a.hasOwnProperty(W)&&(l=a[W],l!==void 0&&Yc(e,t,W,l,a,void 0));return}}for(y in a)a.hasOwnProperty(y)&&(l=a[y],l!=null&&lt(e,t,y,l,a,null))}function Hb(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,c=null,m=null,y=null,z=null,H=null,W=null;for(V in a){var ae=a[V];if(a.hasOwnProperty(V)&&ae!=null)switch(V){case"checked":break;case"value":break;case"defaultValue":z=ae;default:l.hasOwnProperty(V)||lt(e,t,V,null,l,ae)}}for(var q in l){var V=l[q];if(ae=a[q],l.hasOwnProperty(q)&&(V!=null||ae!=null))switch(q){case"type":c=V;break;case"name":s=V;break;case"checked":H=V;break;case"defaultChecked":W=V;break;case"value":m=V;break;case"defaultValue":y=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(i(137,t));break;default:V!==ae&&lt(e,t,q,V,l,ae)}}lu(e,m,y,z,H,W,c,s);return;case"select":V=m=y=q=null;for(c in a)if(z=a[c],a.hasOwnProperty(c)&&z!=null)switch(c){case"value":break;case"multiple":V=z;default:l.hasOwnProperty(c)||lt(e,t,c,null,l,z)}for(s in l)if(c=l[s],z=a[s],l.hasOwnProperty(s)&&(c!=null||z!=null))switch(s){case"value":q=c;break;case"defaultValue":y=c;break;case"multiple":m=c;default:c!==z&&lt(e,t,s,c,l,z)}t=y,a=m,l=V,q!=null?wr(e,!!a,q,!1):!!l!=!!a&&(t!=null?wr(e,!!a,t,!0):wr(e,!!a,a?[]:"",!1));return;case"textarea":V=q=null;for(y in a)if(s=a[y],a.hasOwnProperty(y)&&s!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:lt(e,t,y,null,l,s)}for(m in l)if(s=l[m],c=a[m],l.hasOwnProperty(m)&&(s!=null||c!=null))switch(m){case"value":q=s;break;case"defaultValue":V=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(i(91));break;default:s!==c&&lt(e,t,m,s,l,c)}Yd(e,q,V);return;case"option":for(var be in a)if(q=a[be],a.hasOwnProperty(be)&&q!=null&&!l.hasOwnProperty(be))switch(be){case"selected":e.selected=!1;break;default:lt(e,t,be,null,l,q)}for(z in l)if(q=l[z],V=a[z],l.hasOwnProperty(z)&&q!==V&&(q!=null||V!=null))switch(z){case"selected":e.selected=q&&typeof q!="function"&&typeof q!="symbol";break;default:lt(e,t,z,q,l,V)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ye in a)q=a[ye],a.hasOwnProperty(ye)&&q!=null&&!l.hasOwnProperty(ye)&&lt(e,t,ye,null,l,q);for(H in l)if(q=l[H],V=a[H],l.hasOwnProperty(H)&&q!==V&&(q!=null||V!=null))switch(H){case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(i(137,t));break;default:lt(e,t,H,q,l,V)}return;default:if(iu(t)){for(var ot in a)q=a[ot],a.hasOwnProperty(ot)&&q!==void 0&&!l.hasOwnProperty(ot)&&Yc(e,t,ot,void 0,l,q);for(W in l)q=l[W],V=a[W],!l.hasOwnProperty(W)||q===V||q===void 0&&V===void 0||Yc(e,t,W,q,l,V);return}}for(var $ in a)q=a[$],a.hasOwnProperty($)&&q!=null&&!l.hasOwnProperty($)&&lt(e,t,$,null,l,q);for(ae in l)q=l[ae],V=a[ae],!l.hasOwnProperty(ae)||q===V||q==null&&V==null||lt(e,t,ae,q,l,V)}var Ic=null,Xc=null;function Xi(e){return e.nodeType===9?e:e.ownerDocument}function Xh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Kh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Kc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Qc=null;function qb(){var e=window.event;return e&&e.type==="popstate"?e===Qc?!1:(Qc=e,!0):(Qc=null,!1)}var Qh=typeof setTimeout=="function"?setTimeout:void 0,Pb=typeof clearTimeout=="function"?clearTimeout:void 0,Fh=typeof Promise=="function"?Promise:void 0,Vb=typeof queueMicrotask=="function"?queueMicrotask:typeof Fh<"u"?function(e){return Fh.resolve(null).then(e).catch(Gb)}:Qh;function Gb(e){setTimeout(function(){throw e})}function $a(e){return e==="head"}function Zh(e,t){var a=t,l=0,s=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<l&&8>l){a=l;var m=e.ownerDocument;if(a&1&&so(m.documentElement),a&2&&so(m.body),a&4)for(a=m.head,so(a),m=a.firstChild;m;){var y=m.nextSibling,z=m.nodeName;m[Tl]||z==="SCRIPT"||z==="STYLE"||z==="LINK"&&m.rel.toLowerCase()==="stylesheet"||a.removeChild(m),m=y}}if(s===0){e.removeChild(c),yo(t);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:l=a.charCodeAt(0)-48;else l=0;a=c}while(a);yo(t)}function Fc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Fc(a),tu(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Yb(e,t,a,l){for(;e.nodeType===1;){var s=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Tl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=kn(e.nextSibling),e===null)break}return null}function Ib(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=kn(e.nextSibling),e===null))return null;return e}function Zc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Xb(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function kn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Wc=null;function Wh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Jh(e,t,a){switch(t=Xi(a),e){case"html":if(e=t.documentElement,!e)throw Error(i(452));return e;case"head":if(e=t.head,!e)throw Error(i(453));return e;case"body":if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function so(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);tu(e)}var En=new Map,eg=new Set;function Ki(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ca=K.d;K.d={f:Kb,r:Qb,D:Fb,C:Zb,L:Wb,m:Jb,X:t1,S:e1,M:n1};function Kb(){var e=ca.f(),t=Ui();return e||t}function Qb(e){var t=Rr(e);t!==null&&t.tag===5&&t.type==="form"?Sm(t):ca.r(e)}var ll=typeof document>"u"?null:document;function tg(e,t,a){var l=ll;if(l&&typeof t=="string"&&t){var s=yn(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),eg.has(s)||(eg.add(s),e={rel:e,crossOrigin:a,href:t},l.querySelector(s)===null&&(t=l.createElement("link"),Vt(t,"link",e),Nt(t),l.head.appendChild(t)))}}function Fb(e){ca.D(e),tg("dns-prefetch",e,null)}function Zb(e,t){ca.C(e,t),tg("preconnect",e,t)}function Wb(e,t,a){ca.L(e,t,a);var l=ll;if(l&&e&&t){var s='link[rel="preload"][as="'+yn(t)+'"]';t==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+yn(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+yn(a.imageSizes)+'"]')):s+='[href="'+yn(e)+'"]';var c=s;switch(t){case"style":c=ol(e);break;case"script":c=il(e)}En.has(c)||(e=v({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),En.set(c,e),l.querySelector(s)!==null||t==="style"&&l.querySelector(uo(c))||t==="script"&&l.querySelector(co(c))||(t=l.createElement("link"),Vt(t,"link",e),Nt(t),l.head.appendChild(t)))}}function Jb(e,t){ca.m(e,t);var a=ll;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+yn(l)+'"][href="'+yn(e)+'"]',c=s;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=il(e)}if(!En.has(c)&&(e=v({rel:"modulepreload",href:e},t),En.set(c,e),a.querySelector(s)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(co(c)))return}l=a.createElement("link"),Vt(l,"link",e),Nt(l),a.head.appendChild(l)}}}function e1(e,t,a){ca.S(e,t,a);var l=ll;if(l&&e){var s=Ar(l).hoistableStyles,c=ol(e);t=t||"default";var m=s.get(c);if(!m){var y={loading:0,preload:null};if(m=l.querySelector(uo(c)))y.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},a),(a=En.get(c))&&Jc(e,a);var z=m=l.createElement("link");Nt(z),Vt(z,"link",e),z._p=new Promise(function(H,W){z.onload=H,z.onerror=W}),z.addEventListener("load",function(){y.loading|=1}),z.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Qi(m,t,l)}m={type:"stylesheet",instance:m,count:1,state:y},s.set(c,m)}}}function t1(e,t){ca.X(e,t);var a=ll;if(a&&e){var l=Ar(a).hoistableScripts,s=il(e),c=l.get(s);c||(c=a.querySelector(co(s)),c||(e=v({src:e,async:!0},t),(t=En.get(s))&&ef(e,t),c=a.createElement("script"),Nt(c),Vt(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(s,c))}}function n1(e,t){ca.M(e,t);var a=ll;if(a&&e){var l=Ar(a).hoistableScripts,s=il(e),c=l.get(s);c||(c=a.querySelector(co(s)),c||(e=v({src:e,async:!0,type:"module"},t),(t=En.get(s))&&ef(e,t),c=a.createElement("script"),Nt(c),Vt(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(s,c))}}function ng(e,t,a,l){var s=(s=se.current)?Ki(s):null;if(!s)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=ol(a.href),a=Ar(s).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=ol(a.href);var c=Ar(s).hoistableStyles,m=c.get(e);if(m||(s=s.ownerDocument||s,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,m),(c=s.querySelector(uo(e)))&&!c._p&&(m.instance=c,m.state.loading=5),En.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},En.set(e,a),c||a1(s,e,a,m.state))),t&&l===null)throw Error(i(528,""));return m}if(t&&l!==null)throw Error(i(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=il(a),a=Ar(s).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function ol(e){return'href="'+yn(e)+'"'}function uo(e){return'link[rel="stylesheet"]['+e+"]"}function ag(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function a1(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Vt(t,"link",a),Nt(t),e.head.appendChild(t))}function il(e){return'[src="'+yn(e)+'"]'}function co(e){return"script[async]"+e}function rg(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+yn(a.href)+'"]');if(l)return t.instance=l,Nt(l),l;var s=v({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Nt(l),Vt(l,"style",s),Qi(l,a.precedence,e),t.instance=l;case"stylesheet":s=ol(a.href);var c=e.querySelector(uo(s));if(c)return t.state.loading|=4,t.instance=c,Nt(c),c;l=ag(a),(s=En.get(s))&&Jc(l,s),c=(e.ownerDocument||e).createElement("link"),Nt(c);var m=c;return m._p=new Promise(function(y,z){m.onload=y,m.onerror=z}),Vt(c,"link",l),t.state.loading|=4,Qi(c,a.precedence,e),t.instance=c;case"script":return c=il(a.src),(s=e.querySelector(co(c)))?(t.instance=s,Nt(s),s):(l=a,(s=En.get(c))&&(l=v({},a),ef(l,s)),e=e.ownerDocument||e,s=e.createElement("script"),Nt(s),Vt(s,"link",l),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(i(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Qi(l,a.precedence,e));return t.instance}function Qi(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=l.length?l[l.length-1]:null,c=s,m=0;m<l.length;m++){var y=l[m];if(y.dataset.precedence===t)c=y;else if(c!==s)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Jc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ef(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Fi=null;function lg(e,t,a){if(Fi===null){var l=new Map,s=Fi=new Map;s.set(a,l)}else s=Fi,l=s.get(a),l||(l=new Map,s.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),s=0;s<a.length;s++){var c=a[s];if(!(c[Tl]||c[Yt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var m=c.getAttribute(t)||"";m=e+m;var y=l.get(m);y?y.push(c):l.set(m,[c])}}return l}function og(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function r1(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function ig(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var fo=null;function l1(){}function o1(e,t,a){if(fo===null)throw Error(i(475));var l=fo;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=ol(a.href),c=e.querySelector(uo(s));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Zi.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,Nt(c);return}c=e.ownerDocument||e,a=ag(a),(s=En.get(s))&&Jc(a,s),c=c.createElement("link"),Nt(c);var m=c;m._p=new Promise(function(y,z){m.onload=y,m.onerror=z}),Vt(c,"link",a),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Zi.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function i1(){if(fo===null)throw Error(i(475));var e=fo;return e.stylesheets&&e.count===0&&tf(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&tf(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Zi(){if(this.count--,this.count===0){if(this.stylesheets)tf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Wi=null;function tf(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Wi=new Map,t.forEach(s1,e),Wi=null,Zi.call(e))}function s1(e,t){if(!(t.state.loading&4)){var a=Wi.get(e);if(a)var l=a.get(null);else{a=new Map,Wi.set(e,a);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<s.length;c++){var m=s[c];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(a.set(m.dataset.precedence,m),l=m)}l&&a.set(null,l)}s=t.instance,m=s.getAttribute("data-precedence"),c=a.get(m)||l,c===l&&a.set(null,s),a.set(m,s),this.count++,l=Zi.bind(this),s.addEventListener("load",l),s.addEventListener("error",l),c?c.parentNode.insertBefore(s,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var po={$$typeof:B,Provider:null,Consumer:null,_currentValue:oe,_currentValue2:oe,_threadCount:0};function u1(e,t,a,l,s,c,m,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.hiddenUpdates=Zs(null),this.identifierPrefix=l,this.onUncaughtError=s,this.onCaughtError=c,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function sg(e,t,a,l,s,c,m,y,z,H,W,ae){return e=new u1(e,t,a,m,y,z,H,ae),t=1,c===!0&&(t|=24),c=on(3,null,null,t),e.current=c,c.stateNode=e,t=_u(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:a,cache:t},Hu(c),e}function ug(e){return e?(e=Lr,e):Lr}function cg(e,t,a,l,s,c){s=ug(s),l.context===null?l.context=s:l.pendingContext=s,l=Ea(t),l.payload={element:a},c=c===void 0?null:c,c!==null&&(l.callback=c),a=Ra(e,l,t),a!==null&&(dn(a,e,t),Pl(a,e,t))}function fg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function nf(e,t){fg(e,t),(e=e.alternate)&&fg(e,t)}function dg(e){if(e.tag===13){var t=$r(e,67108864);t!==null&&dn(t,e,67108864),nf(e,67108864)}}var Ji=!0;function c1(e,t,a,l){var s=w.T;w.T=null;var c=K.p;try{K.p=2,af(e,t,a,l)}finally{K.p=c,w.T=s}}function f1(e,t,a,l){var s=w.T;w.T=null;var c=K.p;try{K.p=8,af(e,t,a,l)}finally{K.p=c,w.T=s}}function af(e,t,a,l){if(Ji){var s=rf(l);if(s===null)Gc(e,t,l,es,a),mg(e,l);else if(p1(s,e,t,a,l))l.stopPropagation();else if(mg(e,l),t&4&&-1<d1.indexOf(e)){for(;s!==null;){var c=Rr(s);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var m=he(c.pendingLanes);if(m!==0){var y=c;for(y.pendingLanes|=2,y.entangledLanes|=2;m;){var z=1<<31-qe(m);y.entanglements[1]|=z,m&=~z}Ln(c),(Fe&6)===0&&($i=Qe()+500,lo(0))}}break;case 13:y=$r(c,2),y!==null&&dn(y,c,2),Ui(),nf(c,2)}if(c=rf(l),c===null&&Gc(e,t,l,es,a),c===s)break;s=c}s!==null&&l.stopPropagation()}else Gc(e,t,l,null,a)}}function rf(e){return e=uu(e),lf(e)}var es=null;function lf(e){if(es=null,e=Er(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return es=e,null}function pg(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(pt()){case dt:return 2;case yt:return 8;case tt:case pe:return 32;case rn:return 268435456;default:return 32}default:return 32}}var of=!1,La=null,Ua=null,Ha=null,mo=new Map,ho=new Map,qa=[],d1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function mg(e,t){switch(e){case"focusin":case"focusout":La=null;break;case"dragenter":case"dragleave":Ua=null;break;case"mouseover":case"mouseout":Ha=null;break;case"pointerover":case"pointerout":mo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ho.delete(t.pointerId)}}function go(e,t,a,l,s,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:c,targetContainers:[s]},t!==null&&(t=Rr(t),t!==null&&dg(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function p1(e,t,a,l,s){switch(t){case"focusin":return La=go(La,e,t,a,l,s),!0;case"dragenter":return Ua=go(Ua,e,t,a,l,s),!0;case"mouseover":return Ha=go(Ha,e,t,a,l,s),!0;case"pointerover":var c=s.pointerId;return mo.set(c,go(mo.get(c)||null,e,t,a,l,s)),!0;case"gotpointercapture":return c=s.pointerId,ho.set(c,go(ho.get(c)||null,e,t,a,l,s)),!0}return!1}function hg(e){var t=Er(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,ov(e.priority,function(){if(a.tag===13){var l=fn();l=Ws(l);var s=$r(a,l);s!==null&&dn(s,a,l),nf(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ts(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=rf(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);su=l,a.target.dispatchEvent(l),su=null}else return t=Rr(a),t!==null&&dg(t),e.blockedOn=a,!1;t.shift()}return!0}function gg(e,t,a){ts(e)&&a.delete(t)}function m1(){of=!1,La!==null&&ts(La)&&(La=null),Ua!==null&&ts(Ua)&&(Ua=null),Ha!==null&&ts(Ha)&&(Ha=null),mo.forEach(gg),ho.forEach(gg)}function ns(e,t){e.blockedOn===t&&(e.blockedOn=null,of||(of=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,m1)))}var as=null;function yg(e){as!==e&&(as=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){as===e&&(as=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],s=e[t+2];if(typeof l!="function"){if(lf(l||a)===null)continue;break}var c=Rr(a);c!==null&&(e.splice(t,3),t-=3,lc(c,{pending:!0,data:s,method:a.method,action:l},l,s))}}))}function yo(e){function t(z){return ns(z,e)}La!==null&&ns(La,e),Ua!==null&&ns(Ua,e),Ha!==null&&ns(Ha,e),mo.forEach(t),ho.forEach(t);for(var a=0;a<qa.length;a++){var l=qa[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<qa.length&&(a=qa[0],a.blockedOn===null);)hg(a),a.blockedOn===null&&qa.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var s=a[l],c=a[l+1],m=s[Wt]||null;if(typeof c=="function")m||yg(a);else if(m){var y=null;if(c&&c.hasAttribute("formAction")){if(s=c,m=c[Wt]||null)y=m.formAction;else if(lf(s)!==null)continue}else y=m.action;typeof y=="function"?a[l+1]=y:(a.splice(l,3),l-=3),yg(a)}}}function sf(e){this._internalRoot=e}rs.prototype.render=sf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var a=t.current,l=fn();cg(a,l,e,t,null,null)},rs.prototype.unmount=sf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;cg(e.current,2,null,e,null,null),Ui(),t[Tr]=null}};function rs(e){this._internalRoot=e}rs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Dd();e={blockedOn:null,target:e,priority:t};for(var a=0;a<qa.length&&t!==0&&t<qa[a].priority;a++);qa.splice(a,0,e),a===0&&hg(e)}};var vg=r.version;if(vg!=="19.1.1")throw Error(i(527,vg,"19.1.1"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=g(t),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var h1={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:w,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ls=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ls.isDisabled&&ls.supportsFiber)try{mt=ls.inject(h1),Ee=ls}catch{}}return bo.createRoot=function(e,t){if(!u(e))throw Error(i(299));var a=!1,l="",s=Dm,c=Nm,m=_m,y=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(m=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=sg(e,1,!1,null,null,a,l,s,c,m,y,null),e[Tr]=t.current,Vc(e),new sf(t)},bo.hydrateRoot=function(e,t,a){if(!u(e))throw Error(i(299));var l=!1,s="",c=Dm,m=Nm,y=_m,z=null,H=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(m=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(z=a.unstable_transitionCallbacks),a.formState!==void 0&&(H=a.formState)),t=sg(e,1,!0,t,a??null,l,s,c,m,y,z,H),t.context=ug(null),a=t.current,l=fn(),l=Ws(l),s=Ea(l),s.callback=null,Ra(a,s,l),a=l,t.current.lanes=a,Cl(t,a),Ln(t),e[Tr]=t.current,Vc(e),new rs(t)},bo.version="19.1.1",bo}var Og;function R1(){if(Og)return ff.exports;Og=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),ff.exports=E1(),ff.exports}var A1=R1();function pa(n,...r){const o=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(i=>o.searchParams.append("args[]",i)),`Minified MUI error #${n}; visit ${o} for the full message.`}const In="$$material";function bs(){return bs=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)({}).hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},bs.apply(null,arguments)}function M1(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function O1(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var w1=(function(){function n(o){var i=this;this._insertTag=function(u){var f;i.tags.length===0?i.insertionPoint?f=i.insertionPoint.nextSibling:i.prepend?f=i.container.firstChild:f=i.before:f=i.tags[i.tags.length-1].nextSibling,i.container.insertBefore(u,f),i.tags.push(u)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(i){i.forEach(this._insertTag)},r.insert=function(i){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(O1(this));var u=this.tags[this.tags.length-1];if(this.isSpeedy){var f=M1(u);try{f.insertRule(i,f.cssRules.length)}catch{}}else u.appendChild(document.createTextNode(i));this.ctr++},r.flush=function(){this.tags.forEach(function(i){var u;return(u=i.parentNode)==null?void 0:u.removeChild(i)}),this.tags=[],this.ctr=0},n})(),Kt="-ms-",Ss="-moz-",Ve="-webkit-",Iy="comm",Jf="rule",ed="decl",z1="@import",Xy="@keyframes",k1="@layer",j1=Math.abs,Os=String.fromCharCode,B1=Object.assign;function D1(n,r){return Gt(n,0)^45?(((r<<2^Gt(n,0))<<2^Gt(n,1))<<2^Gt(n,2))<<2^Gt(n,3):0}function Ky(n){return n.trim()}function N1(n,r){return(n=r.exec(n))?n[0]:n}function Ge(n,r,o){return n.replace(r,o)}function kf(n,r){return n.indexOf(r)}function Gt(n,r){return n.charCodeAt(r)|0}function Bo(n,r,o){return n.slice(r,o)}function qn(n){return n.length}function td(n){return n.length}function os(n,r){return r.push(n),n}function _1(n,r){return n.map(r).join("")}var ws=1,vl=1,Qy=0,an=0,kt=0,Sl="";function zs(n,r,o,i,u,f,d){return{value:n,root:r,parent:o,type:i,props:u,children:f,line:ws,column:vl,length:d,return:""}}function So(n,r){return B1(zs("",null,null,"",null,null,0),n,{length:-n.length},r)}function $1(){return kt}function L1(){return kt=an>0?Gt(Sl,--an):0,vl--,kt===10&&(vl=1,ws--),kt}function mn(){return kt=an<Qy?Gt(Sl,an++):0,vl++,kt===10&&(vl=1,ws++),kt}function Xn(){return Gt(Sl,an)}function ms(){return an}function Ho(n,r){return Bo(Sl,n,r)}function Do(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Fy(n){return ws=vl=1,Qy=qn(Sl=n),an=0,[]}function Zy(n){return Sl="",n}function hs(n){return Ky(Ho(an-1,jf(n===91?n+2:n===40?n+1:n)))}function U1(n){for(;(kt=Xn())&&kt<33;)mn();return Do(n)>2||Do(kt)>3?"":" "}function H1(n,r){for(;--r&&mn()&&!(kt<48||kt>102||kt>57&&kt<65||kt>70&&kt<97););return Ho(n,ms()+(r<6&&Xn()==32&&mn()==32))}function jf(n){for(;mn();)switch(kt){case n:return an;case 34:case 39:n!==34&&n!==39&&jf(kt);break;case 40:n===41&&jf(n);break;case 92:mn();break}return an}function q1(n,r){for(;mn()&&n+kt!==57;)if(n+kt===84&&Xn()===47)break;return"/*"+Ho(r,an-1)+"*"+Os(n===47?n:mn())}function P1(n){for(;!Do(Xn());)mn();return Ho(n,an)}function V1(n){return Zy(gs("",null,null,null,[""],n=Fy(n),0,[0],n))}function gs(n,r,o,i,u,f,d,p,g){for(var h=0,v=0,x=d,R=0,A=0,T=0,E=1,j=1,N=1,U=0,B="",k=u,M=f,_=i,Y=B;j;)switch(T=U,U=mn()){case 40:if(T!=108&&Gt(Y,x-1)==58){kf(Y+=Ge(hs(U),"&","&\f"),"&\f")!=-1&&(N=-1);break}case 34:case 39:case 91:Y+=hs(U);break;case 9:case 10:case 13:case 32:Y+=U1(T);break;case 92:Y+=H1(ms()-1,7);continue;case 47:switch(Xn()){case 42:case 47:os(G1(q1(mn(),ms()),r,o),g);break;default:Y+="/"}break;case 123*E:p[h++]=qn(Y)*N;case 125*E:case 59:case 0:switch(U){case 0:case 125:j=0;case 59+v:N==-1&&(Y=Ge(Y,/\f/g,"")),A>0&&qn(Y)-x&&os(A>32?zg(Y+";",i,o,x-1):zg(Ge(Y," ","")+";",i,o,x-2),g);break;case 59:Y+=";";default:if(os(_=wg(Y,r,o,h,v,u,p,B,k=[],M=[],x),f),U===123)if(v===0)gs(Y,r,_,_,k,f,x,p,M);else switch(R===99&&Gt(Y,3)===110?100:R){case 100:case 108:case 109:case 115:gs(n,_,_,i&&os(wg(n,_,_,0,0,u,p,B,u,k=[],x),M),u,M,x,p,i?k:M);break;default:gs(Y,_,_,_,[""],M,0,p,M)}}h=v=A=0,E=N=1,B=Y="",x=d;break;case 58:x=1+qn(Y),A=T;default:if(E<1){if(U==123)--E;else if(U==125&&E++==0&&L1()==125)continue}switch(Y+=Os(U),U*E){case 38:N=v>0?1:(Y+="\f",-1);break;case 44:p[h++]=(qn(Y)-1)*N,N=1;break;case 64:Xn()===45&&(Y+=hs(mn())),R=Xn(),v=x=qn(B=Y+=P1(ms())),U++;break;case 45:T===45&&qn(Y)==2&&(E=0)}}return f}function wg(n,r,o,i,u,f,d,p,g,h,v){for(var x=u-1,R=u===0?f:[""],A=td(R),T=0,E=0,j=0;T<i;++T)for(var N=0,U=Bo(n,x+1,x=j1(E=d[T])),B=n;N<A;++N)(B=Ky(E>0?R[N]+" "+U:Ge(U,/&\f/g,R[N])))&&(g[j++]=B);return zs(n,r,o,u===0?Jf:p,g,h,v)}function G1(n,r,o){return zs(n,r,o,Iy,Os($1()),Bo(n,2,-2),0)}function zg(n,r,o,i){return zs(n,r,o,ed,Bo(n,0,i),Bo(n,i+1,-1),i)}function hl(n,r){for(var o="",i=td(n),u=0;u<i;u++)o+=r(n[u],u,n,r)||"";return o}function Y1(n,r,o,i){switch(n.type){case k1:if(n.children.length)break;case z1:case ed:return n.return=n.return||n.value;case Iy:return"";case Xy:return n.return=n.value+"{"+hl(n.children,i)+"}";case Jf:n.value=n.props.join(",")}return qn(o=hl(n.children,i))?n.return=n.value+"{"+o+"}":""}function I1(n){var r=td(n);return function(o,i,u,f){for(var d="",p=0;p<r;p++)d+=n[p](o,i,u,f)||"";return d}}function X1(n){return function(r){r.root||(r=r.return)&&n(r)}}function Wy(n){var r=Object.create(null);return function(o){return r[o]===void 0&&(r[o]=n(o)),r[o]}}var K1=function(r,o,i){for(var u=0,f=0;u=f,f=Xn(),u===38&&f===12&&(o[i]=1),!Do(f);)mn();return Ho(r,an)},Q1=function(r,o){var i=-1,u=44;do switch(Do(u)){case 0:u===38&&Xn()===12&&(o[i]=1),r[i]+=K1(an-1,o,i);break;case 2:r[i]+=hs(u);break;case 4:if(u===44){r[++i]=Xn()===58?"&\f":"",o[i]=r[i].length;break}default:r[i]+=Os(u)}while(u=mn());return r},F1=function(r,o){return Zy(Q1(Fy(r),o))},kg=new WeakMap,Z1=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var o=r.value,i=r.parent,u=r.column===i.column&&r.line===i.line;i.type!=="rule";)if(i=i.parent,!i)return;if(!(r.props.length===1&&o.charCodeAt(0)!==58&&!kg.get(i))&&!u){kg.set(r,!0);for(var f=[],d=F1(o,f),p=i.props,g=0,h=0;g<d.length;g++)for(var v=0;v<p.length;v++,h++)r.props[h]=f[g]?d[g].replace(/&\f/g,p[v]):p[v]+" "+d[g]}}},W1=function(r){if(r.type==="decl"){var o=r.value;o.charCodeAt(0)===108&&o.charCodeAt(2)===98&&(r.return="",r.value="")}};function Jy(n,r){switch(D1(n,r)){case 5103:return Ve+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ve+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return Ve+n+Ss+n+Kt+n+n;case 6828:case 4268:return Ve+n+Kt+n+n;case 6165:return Ve+n+Kt+"flex-"+n+n;case 5187:return Ve+n+Ge(n,/(\w+).+(:[^]+)/,Ve+"box-$1$2"+Kt+"flex-$1$2")+n;case 5443:return Ve+n+Kt+"flex-item-"+Ge(n,/flex-|-self/,"")+n;case 4675:return Ve+n+Kt+"flex-line-pack"+Ge(n,/align-content|flex-|-self/,"")+n;case 5548:return Ve+n+Kt+Ge(n,"shrink","negative")+n;case 5292:return Ve+n+Kt+Ge(n,"basis","preferred-size")+n;case 6060:return Ve+"box-"+Ge(n,"-grow","")+Ve+n+Kt+Ge(n,"grow","positive")+n;case 4554:return Ve+Ge(n,/([^-])(transform)/g,"$1"+Ve+"$2")+n;case 6187:return Ge(Ge(Ge(n,/(zoom-|grab)/,Ve+"$1"),/(image-set)/,Ve+"$1"),n,"")+n;case 5495:case 3959:return Ge(n,/(image-set\([^]*)/,Ve+"$1$`$1");case 4968:return Ge(Ge(n,/(.+:)(flex-)?(.*)/,Ve+"box-pack:$3"+Kt+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ve+n+n;case 4095:case 3583:case 4068:case 2532:return Ge(n,/(.+)-inline(.+)/,Ve+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(qn(n)-1-r>6)switch(Gt(n,r+1)){case 109:if(Gt(n,r+4)!==45)break;case 102:return Ge(n,/(.+:)(.+)-([^]+)/,"$1"+Ve+"$2-$3$1"+Ss+(Gt(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~kf(n,"stretch")?Jy(Ge(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(Gt(n,r+1)!==115)break;case 6444:switch(Gt(n,qn(n)-3-(~kf(n,"!important")&&10))){case 107:return Ge(n,":",":"+Ve)+n;case 101:return Ge(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ve+(Gt(n,14)===45?"inline-":"")+"box$3$1"+Ve+"$2$3$1"+Kt+"$2box$3")+n}break;case 5936:switch(Gt(n,r+11)){case 114:return Ve+n+Kt+Ge(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return Ve+n+Kt+Ge(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return Ve+n+Kt+Ge(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return Ve+n+Kt+n+n}return n}var J1=function(r,o,i,u){if(r.length>-1&&!r.return)switch(r.type){case ed:r.return=Jy(r.value,r.length);break;case Xy:return hl([So(r,{value:Ge(r.value,"@","@"+Ve)})],u);case Jf:if(r.length)return _1(r.props,function(f){switch(N1(f,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return hl([So(r,{props:[Ge(f,/:(read-\w+)/,":"+Ss+"$1")]})],u);case"::placeholder":return hl([So(r,{props:[Ge(f,/:(plac\w+)/,":"+Ve+"input-$1")]}),So(r,{props:[Ge(f,/:(plac\w+)/,":"+Ss+"$1")]}),So(r,{props:[Ge(f,/:(plac\w+)/,Kt+"input-$1")]})],u)}return""})}},eS=[J1],tS=function(r){var o=r.key;if(o==="css"){var i=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(i,function(E){var j=E.getAttribute("data-emotion");j.indexOf(" ")!==-1&&(document.head.appendChild(E),E.setAttribute("data-s",""))})}var u=r.stylisPlugins||eS,f={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(E){for(var j=E.getAttribute("data-emotion").split(" "),N=1;N<j.length;N++)f[j[N]]=!0;p.push(E)});var g,h=[Z1,W1];{var v,x=[Y1,X1(function(E){v.insert(E)})],R=I1(h.concat(u,x)),A=function(j){return hl(V1(j),R)};g=function(j,N,U,B){v=U,A(j?j+"{"+N.styles+"}":N.styles),B&&(T.inserted[N.name]=!0)}}var T={key:o,sheet:new w1({key:o,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:f,registered:{},insert:g};return T.sheet.hydrate(p),T},hf={exports:{}},Xe={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jg;function nS(){if(jg)return Xe;jg=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,f=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,g=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,v=n?Symbol.for("react.forward_ref"):60112,x=n?Symbol.for("react.suspense"):60113,R=n?Symbol.for("react.suspense_list"):60120,A=n?Symbol.for("react.memo"):60115,T=n?Symbol.for("react.lazy"):60116,E=n?Symbol.for("react.block"):60121,j=n?Symbol.for("react.fundamental"):60117,N=n?Symbol.for("react.responder"):60118,U=n?Symbol.for("react.scope"):60119;function B(M){if(typeof M=="object"&&M!==null){var _=M.$$typeof;switch(_){case r:switch(M=M.type,M){case g:case h:case i:case f:case u:case x:return M;default:switch(M=M&&M.$$typeof,M){case p:case v:case T:case A:case d:return M;default:return _}}case o:return _}}}function k(M){return B(M)===h}return Xe.AsyncMode=g,Xe.ConcurrentMode=h,Xe.ContextConsumer=p,Xe.ContextProvider=d,Xe.Element=r,Xe.ForwardRef=v,Xe.Fragment=i,Xe.Lazy=T,Xe.Memo=A,Xe.Portal=o,Xe.Profiler=f,Xe.StrictMode=u,Xe.Suspense=x,Xe.isAsyncMode=function(M){return k(M)||B(M)===g},Xe.isConcurrentMode=k,Xe.isContextConsumer=function(M){return B(M)===p},Xe.isContextProvider=function(M){return B(M)===d},Xe.isElement=function(M){return typeof M=="object"&&M!==null&&M.$$typeof===r},Xe.isForwardRef=function(M){return B(M)===v},Xe.isFragment=function(M){return B(M)===i},Xe.isLazy=function(M){return B(M)===T},Xe.isMemo=function(M){return B(M)===A},Xe.isPortal=function(M){return B(M)===o},Xe.isProfiler=function(M){return B(M)===f},Xe.isStrictMode=function(M){return B(M)===u},Xe.isSuspense=function(M){return B(M)===x},Xe.isValidElementType=function(M){return typeof M=="string"||typeof M=="function"||M===i||M===h||M===f||M===u||M===x||M===R||typeof M=="object"&&M!==null&&(M.$$typeof===T||M.$$typeof===A||M.$$typeof===d||M.$$typeof===p||M.$$typeof===v||M.$$typeof===j||M.$$typeof===N||M.$$typeof===U||M.$$typeof===E)},Xe.typeOf=B,Xe}var Bg;function aS(){return Bg||(Bg=1,hf.exports=nS()),hf.exports}var gf,Dg;function rS(){if(Dg)return gf;Dg=1;var n=aS(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},f={};f[n.ForwardRef]=i,f[n.Memo]=u;function d(T){return n.isMemo(T)?u:f[T.$$typeof]||r}var p=Object.defineProperty,g=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,v=Object.getOwnPropertyDescriptor,x=Object.getPrototypeOf,R=Object.prototype;function A(T,E,j){if(typeof E!="string"){if(R){var N=x(E);N&&N!==R&&A(T,N,j)}var U=g(E);h&&(U=U.concat(h(E)));for(var B=d(T),k=d(E),M=0;M<U.length;++M){var _=U[M];if(!o[_]&&!(j&&j[_])&&!(k&&k[_])&&!(B&&B[_])){var Y=v(E,_);try{p(T,_,Y)}catch{}}}}return T}return gf=A,gf}rS();var lS=!0;function e0(n,r,o){var i="";return o.split(" ").forEach(function(u){n[u]!==void 0?r.push(n[u]+";"):u&&(i+=u+" ")}),i}var nd=function(r,o,i){var u=r.key+"-"+o.name;(i===!1||lS===!1)&&r.registered[u]===void 0&&(r.registered[u]=o.styles)},ad=function(r,o,i){nd(r,o,i);var u=r.key+"-"+o.name;if(r.inserted[o.name]===void 0){var f=o;do r.insert(o===f?"."+u:"",f,r.sheet,!0),f=f.next;while(f!==void 0)}};function oS(n){for(var r=0,o,i=0,u=n.length;u>=4;++i,u-=4)o=n.charCodeAt(i)&255|(n.charCodeAt(++i)&255)<<8|(n.charCodeAt(++i)&255)<<16|(n.charCodeAt(++i)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,r=(o&65535)*1540483477+((o>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(u){case 3:r^=(n.charCodeAt(i+2)&255)<<16;case 2:r^=(n.charCodeAt(i+1)&255)<<8;case 1:r^=n.charCodeAt(i)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var iS={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},sS=/[A-Z]|^ms/g,uS=/_EMO_([^_]+?)_([^]*?)_EMO_/g,t0=function(r){return r.charCodeAt(1)===45},Ng=function(r){return r!=null&&typeof r!="boolean"},yf=Wy(function(n){return t0(n)?n:n.replace(sS,"-$&").toLowerCase()}),_g=function(r,o){switch(r){case"animation":case"animationName":if(typeof o=="string")return o.replace(uS,function(i,u,f){return Pn={name:u,styles:f,next:Pn},u})}return iS[r]!==1&&!t0(r)&&typeof o=="number"&&o!==0?o+"px":o};function No(n,r,o){if(o==null)return"";var i=o;if(i.__emotion_styles!==void 0)return i;switch(typeof o){case"boolean":return"";case"object":{var u=o;if(u.anim===1)return Pn={name:u.name,styles:u.styles,next:Pn},u.name;var f=o;if(f.styles!==void 0){var d=f.next;if(d!==void 0)for(;d!==void 0;)Pn={name:d.name,styles:d.styles,next:Pn},d=d.next;var p=f.styles+";";return p}return cS(n,r,o)}case"function":{if(n!==void 0){var g=Pn,h=o(n);return Pn=g,No(n,r,h)}break}}var v=o;if(r==null)return v;var x=r[v];return x!==void 0?x:v}function cS(n,r,o){var i="";if(Array.isArray(o))for(var u=0;u<o.length;u++)i+=No(n,r,o[u])+";";else for(var f in o){var d=o[f];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?i+=f+"{"+r[p]+"}":Ng(p)&&(i+=yf(f)+":"+_g(f,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var g=0;g<d.length;g++)Ng(d[g])&&(i+=yf(f)+":"+_g(f,d[g])+";");else{var h=No(n,r,d);switch(f){case"animation":case"animationName":{i+=yf(f)+":"+h+";";break}default:i+=f+"{"+h+"}"}}}return i}var $g=/label:\s*([^\s;{]+)\s*(;|$)/g,Pn;function qo(n,r,o){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var i=!0,u="";Pn=void 0;var f=n[0];if(f==null||f.raw===void 0)i=!1,u+=No(o,r,f);else{var d=f;u+=d[0]}for(var p=1;p<n.length;p++)if(u+=No(o,r,n[p]),i){var g=f;u+=g[p]}$g.lastIndex=0;for(var h="",v;(v=$g.exec(u))!==null;)h+="-"+v[1];var x=oS(u)+h;return{name:x,styles:u,next:Pn}}var fS=function(r){return r()},n0=zf.useInsertionEffect?zf.useInsertionEffect:!1,a0=n0||fS,Lg=n0||C.useLayoutEffect,r0=C.createContext(typeof HTMLElement<"u"?tS({key:"css"}):null);r0.Provider;var rd=function(r){return C.forwardRef(function(o,i){var u=C.useContext(r0);return r(o,u,i)})},Po=C.createContext({}),ld={}.hasOwnProperty,Bf="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",dS=function(r,o){var i={};for(var u in o)ld.call(o,u)&&(i[u]=o[u]);return i[Bf]=r,i},pS=function(r){var o=r.cache,i=r.serialized,u=r.isStringTag;return nd(o,i,u),a0(function(){return ad(o,i,u)}),null},mS=rd(function(n,r,o){var i=n.css;typeof i=="string"&&r.registered[i]!==void 0&&(i=r.registered[i]);var u=n[Bf],f=[i],d="";typeof n.className=="string"?d=e0(r.registered,f,n.className):n.className!=null&&(d=n.className+" ");var p=qo(f,void 0,C.useContext(Po));d+=r.key+"-"+p.name;var g={};for(var h in n)ld.call(n,h)&&h!=="css"&&h!==Bf&&(g[h]=n[h]);return g.className=d,o&&(g.ref=o),C.createElement(C.Fragment,null,C.createElement(pS,{cache:r,serialized:p,isStringTag:typeof u=="string"}),C.createElement(u,g))}),hS=mS,Ug=function(r,o){var i=arguments;if(o==null||!ld.call(o,"css"))return C.createElement.apply(void 0,i);var u=i.length,f=new Array(u);f[0]=hS,f[1]=dS(r,o);for(var d=2;d<u;d++)f[d]=i[d];return C.createElement.apply(null,f)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(Ug||(Ug={}));var gS=rd(function(n,r){var o=n.styles,i=qo([o],void 0,C.useContext(Po)),u=C.useRef();return Lg(function(){var f=r.key+"-global",d=new r.sheet.constructor({key:f,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,g=document.querySelector('style[data-emotion="'+f+" "+i.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),g!==null&&(p=!0,g.setAttribute("data-emotion",f),d.hydrate([g])),u.current=[d,p],function(){d.flush()}},[r]),Lg(function(){var f=u.current,d=f[0],p=f[1];if(p){f[1]=!1;return}if(i.next!==void 0&&ad(r,i.next,!0),d.tags.length){var g=d.tags[d.tags.length-1].nextElementSibling;d.before=g,d.flush()}r.insert("",i,d,!1)},[r,i.name]),null});function od(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return qo(r)}function Vo(){var n=od.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var yS=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|popover|popoverTarget|popoverTargetAction|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,vS=Wy(function(n){return yS.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),bS=vS,SS=function(r){return r!=="theme"},Hg=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?bS:SS},qg=function(r,o,i){var u;if(o){var f=o.shouldForwardProp;u=r.__emotion_forwardProp&&f?function(d){return r.__emotion_forwardProp(d)&&f(d)}:f}return typeof u!="function"&&i&&(u=r.__emotion_forwardProp),u},xS=function(r){var o=r.cache,i=r.serialized,u=r.isStringTag;return nd(o,i,u),a0(function(){return ad(o,i,u)}),null},CS=function n(r,o){var i=r.__emotion_real===r,u=i&&r.__emotion_base||r,f,d;o!==void 0&&(f=o.label,d=o.target);var p=qg(r,o,i),g=p||Hg(u),h=!g("as");return function(){var v=arguments,x=i&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(f!==void 0&&x.push("label:"+f+";"),v[0]==null||v[0].raw===void 0)x.push.apply(x,v);else{var R=v[0];x.push(R[0]);for(var A=v.length,T=1;T<A;T++)x.push(v[T],R[T])}var E=rd(function(j,N,U){var B=h&&j.as||u,k="",M=[],_=j;if(j.theme==null){_={};for(var Y in j)_[Y]=j[Y];_.theme=C.useContext(Po)}typeof j.className=="string"?k=e0(N.registered,M,j.className):j.className!=null&&(k=j.className+" ");var I=qo(x.concat(M),N.registered,_);k+=N.key+"-"+I.name,d!==void 0&&(k+=" "+d);var F=h&&p===void 0?Hg(B):g,J={};for(var te in j)h&&te==="as"||F(te)&&(J[te]=j[te]);return J.className=k,U&&(J.ref=U),C.createElement(C.Fragment,null,C.createElement(xS,{cache:N,serialized:I,isStringTag:typeof B=="string"}),C.createElement(B,J))});return E.displayName=f!==void 0?f:"Styled("+(typeof u=="string"?u:u.displayName||u.name||"Component")+")",E.defaultProps=r.defaultProps,E.__emotion_real=E,E.__emotion_base=u,E.__emotion_styles=x,E.__emotion_forwardProp=p,Object.defineProperty(E,"toString",{value:function(){return"."+d}}),E.withComponent=function(j,N){var U=n(j,bs({},o,N,{shouldForwardProp:qg(E,N,!0)}));return U.apply(void 0,x)},E}},TS=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Df=CS.bind(null);TS.forEach(function(n){Df[n]=Df(n)});function ES(n){return n==null||Object.keys(n).length===0}function l0(n){const{styles:r,defaultTheme:o={}}=n,i=typeof r=="function"?u=>r(ES(u)?o:u):r;return S.jsx(gS,{styles:i})}function o0(n,r){return Df(n,r)}function RS(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const Pg=[];function Ga(n){return Pg[0]=n,qo(Pg)}var vf={exports:{}},it={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vg;function AS(){if(Vg)return it;Vg=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),v=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),R=Symbol.for("react.view_transition"),A=Symbol.for("react.client.reference");function T(E){if(typeof E=="object"&&E!==null){var j=E.$$typeof;switch(j){case n:switch(E=E.type,E){case o:case u:case i:case g:case h:case R:return E;default:switch(E=E&&E.$$typeof,E){case d:case p:case x:case v:return E;case f:return E;default:return j}}case r:return j}}}return it.ContextConsumer=f,it.ContextProvider=d,it.Element=n,it.ForwardRef=p,it.Fragment=o,it.Lazy=x,it.Memo=v,it.Portal=r,it.Profiler=u,it.StrictMode=i,it.Suspense=g,it.SuspenseList=h,it.isContextConsumer=function(E){return T(E)===f},it.isContextProvider=function(E){return T(E)===d},it.isElement=function(E){return typeof E=="object"&&E!==null&&E.$$typeof===n},it.isForwardRef=function(E){return T(E)===p},it.isFragment=function(E){return T(E)===o},it.isLazy=function(E){return T(E)===x},it.isMemo=function(E){return T(E)===v},it.isPortal=function(E){return T(E)===r},it.isProfiler=function(E){return T(E)===u},it.isStrictMode=function(E){return T(E)===i},it.isSuspense=function(E){return T(E)===g},it.isSuspenseList=function(E){return T(E)===h},it.isValidElementType=function(E){return typeof E=="string"||typeof E=="function"||E===o||E===u||E===i||E===g||E===h||typeof E=="object"&&E!==null&&(E.$$typeof===x||E.$$typeof===v||E.$$typeof===d||E.$$typeof===f||E.$$typeof===p||E.$$typeof===A||E.getModuleId!==void 0)},it.typeOf=T,it}var Gg;function MS(){return Gg||(Gg=1,vf.exports=AS()),vf.exports}var i0=MS();function Gn(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function s0(n){if(C.isValidElement(n)||i0.isValidElementType(n)||!Gn(n))return n;const r={};return Object.keys(n).forEach(o=>{r[o]=s0(n[o])}),r}function Ut(n,r,o={clone:!0}){const i=o.clone?{...n}:n;return Gn(n)&&Gn(r)&&Object.keys(r).forEach(u=>{C.isValidElement(r[u])||i0.isValidElementType(r[u])?i[u]=r[u]:Gn(r[u])&&Object.prototype.hasOwnProperty.call(n,u)&&Gn(n[u])?i[u]=Ut(n[u],r[u],o):o.clone?i[u]=Gn(r[u])?s0(r[u]):r[u]:i[u]=r[u]}),i}const OS=n=>{const r=Object.keys(n).map(o=>({key:o,val:n[o]}))||[];return r.sort((o,i)=>o.val-i.val),r.reduce((o,i)=>({...o,[i.key]:i.val}),{})};function wS(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:i=5,...u}=n,f=OS(r),d=Object.keys(f);function p(R){return`@media (min-width:${typeof r[R]=="number"?r[R]:R}${o})`}function g(R){return`@media (max-width:${(typeof r[R]=="number"?r[R]:R)-i/100}${o})`}function h(R,A){const T=d.indexOf(A);return`@media (min-width:${typeof r[R]=="number"?r[R]:R}${o}) and (max-width:${(T!==-1&&typeof r[d[T]]=="number"?r[d[T]]:A)-i/100}${o})`}function v(R){return d.indexOf(R)+1<d.length?h(R,d[d.indexOf(R)+1]):p(R)}function x(R){const A=d.indexOf(R);return A===0?p(d[1]):A===d.length-1?g(d[A]):h(R,d[d.indexOf(R)+1]).replace("@media","@media not all and")}return{keys:d,values:f,up:p,down:g,between:h,only:v,not:x,unit:o,...u}}function Yg(n,r){if(!n.containerQueries)return r;const o=Object.keys(r).filter(i=>i.startsWith("@container")).sort((i,u)=>{const f=/min-width:\s*([0-9.]+)/;return+(i.match(f)?.[1]||0)-+(u.match(f)?.[1]||0)});return o.length?o.reduce((i,u)=>{const f=r[u];return delete i[u],i[u]=f,i},{...r}):r}function zS(n,r){return r==="@"||r.startsWith("@")&&(n.some(o=>r.startsWith(`@${o}`))||!!r.match(/^@\d/))}function kS(n,r){const o=r.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,i,u]=o,f=Number.isNaN(+i)?i||0:+i;return n.containerQueries(u).up(f)}function jS(n){const r=(f,d)=>f.replace("@media",d?`@container ${d}`:"@container");function o(f,d){f.up=(...p)=>r(n.breakpoints.up(...p),d),f.down=(...p)=>r(n.breakpoints.down(...p),d),f.between=(...p)=>r(n.breakpoints.between(...p),d),f.only=(...p)=>r(n.breakpoints.only(...p),d),f.not=(...p)=>{const g=r(n.breakpoints.not(...p),d);return g.includes("not all and")?g.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):g}}const i={},u=f=>(o(i,f),i);return o(u),{...n,containerQueries:u}}const BS={borderRadius:4};function wo(n,r){return r?Ut(n,r,{clone:!1}):n}const ks={xs:0,sm:600,md:900,lg:1200,xl:1536},Ig={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${ks[n]}px)`},DS={containerQueries:n=>({up:r=>{let o=typeof r=="number"?r:ks[r]||r;return typeof o=="number"&&(o=`${o}px`),n?`@container ${n} (min-width:${o})`:`@container (min-width:${o})`}})};function jn(n,r,o){const i=n.theme||{};if(Array.isArray(r)){const f=i.breakpoints||Ig;return r.reduce((d,p,g)=>(d[f.up(f.keys[g])]=o(r[g]),d),{})}if(typeof r=="object"){const f=i.breakpoints||Ig;return Object.keys(r).reduce((d,p)=>{if(zS(f.keys,p)){const g=kS(i.containerQueries?i:DS,p);g&&(d[g]=o(r[p],p))}else if(Object.keys(f.values||ks).includes(p)){const g=f.up(p);d[g]=o(r[p],p)}else{const g=p;d[g]=r[g]}return d},{})}return o(r)}function u0(n={}){return n.keys?.reduce((o,i)=>{const u=n.up(i);return o[u]={},o},{})||{}}function Nf(n,r){return n.reduce((o,i)=>{const u=o[i];return(!u||Object.keys(u).length===0)&&delete o[i],o},r)}function NS(n,...r){const o=u0(n),i=[o,...r].reduce((u,f)=>Ut(u,f),{});return Nf(Object.keys(o),i)}function _S(n,r){if(typeof n!="object")return{};const o={},i=Object.keys(r);return Array.isArray(n)?i.forEach((u,f)=>{f<n.length&&(o[u]=!0)}):i.forEach(u=>{n[u]!=null&&(o[u]=!0)}),o}function bf({values:n,breakpoints:r,base:o}){const i=o||_S(n,r),u=Object.keys(i);if(u.length===0)return n;let f;return u.reduce((d,p,g)=>(Array.isArray(n)?(d[p]=n[g]!=null?n[g]:n[f],f=g):typeof n=="object"?(d[p]=n[p]!=null?n[p]:n[f],f=p):d[p]=n,d),{})}function ie(n){if(typeof n!="string")throw new Error(pa(7));return n.charAt(0).toUpperCase()+n.slice(1)}function Vn(n,r,o=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&o){const i=`vars.${r}`.split(".").reduce((u,f)=>u&&u[f]?u[f]:null,n);if(i!=null)return i}return r.split(".").reduce((i,u)=>i&&i[u]!=null?i[u]:null,n)}function xs(n,r,o,i=o){let u;return typeof n=="function"?u=n(o):Array.isArray(n)?u=n[o]||i:u=Vn(n,o)||i,r&&(u=r(u,i,n)),u}function Ot(n){const{prop:r,cssProperty:o=n.prop,themeKey:i,transform:u}=n,f=d=>{if(d[r]==null)return null;const p=d[r],g=d.theme,h=Vn(g,i)||{};return jn(d,p,x=>{let R=xs(h,u,x);return x===R&&typeof x=="string"&&(R=xs(h,u,`${r}${x==="default"?"":ie(x)}`,x)),o===!1?R:{[o]:R}})};return f.propTypes={},f.filterProps=[r],f}function $S(n){const r={};return o=>(r[o]===void 0&&(r[o]=n(o)),r[o])}const LS={m:"margin",p:"padding"},US={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Xg={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},HS=$S(n=>{if(n.length>2)if(Xg[n])n=Xg[n];else return[n];const[r,o]=n.split(""),i=LS[r],u=US[o]||"";return Array.isArray(u)?u.map(f=>i+f):[i+u]}),id=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],sd=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...id,...sd];function Go(n,r,o,i){const u=Vn(n,r,!0)??o;return typeof u=="number"||typeof u=="string"?f=>typeof f=="string"?f:typeof u=="string"?u.startsWith("var(")&&f===0?0:u.startsWith("var(")&&f===1?u:`calc(${f} * ${u})`:u*f:Array.isArray(u)?f=>{if(typeof f=="string")return f;const d=Math.abs(f),p=u[d];return f>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof u=="function"?u:()=>{}}function js(n){return Go(n,"spacing",8)}function xr(n,r){return typeof r=="string"||r==null?r:n(r)}function qS(n,r){return o=>n.reduce((i,u)=>(i[u]=xr(r,o),i),{})}function PS(n,r,o,i){if(!r.includes(o))return null;const u=HS(o),f=qS(u,i),d=n[o];return jn(n,d,f)}function c0(n,r){const o=js(n.theme);return Object.keys(n).map(i=>PS(n,r,i,o)).reduce(wo,{})}function Et(n){return c0(n,id)}Et.propTypes={};Et.filterProps=id;function Rt(n){return c0(n,sd)}Rt.propTypes={};Rt.filterProps=sd;function f0(n=8,r=js({spacing:n})){if(n.mui)return n;const o=(...i)=>(i.length===0?[1]:i).map(f=>{const d=r(f);return typeof d=="number"?`${d}px`:d}).join(" ");return o.mui=!0,o}function Bs(...n){const r=n.reduce((i,u)=>(u.filterProps.forEach(f=>{i[f]=u}),i),{}),o=i=>Object.keys(i).reduce((u,f)=>r[f]?wo(u,r[f](i)):u,{});return o.propTypes={},o.filterProps=n.reduce((i,u)=>i.concat(u.filterProps),[]),o}function An(n){return typeof n!="number"?n:`${n}px solid`}function Mn(n,r){return Ot({prop:n,themeKey:"borders",transform:r})}const VS=Mn("border",An),GS=Mn("borderTop",An),YS=Mn("borderRight",An),IS=Mn("borderBottom",An),XS=Mn("borderLeft",An),KS=Mn("borderColor"),QS=Mn("borderTopColor"),FS=Mn("borderRightColor"),ZS=Mn("borderBottomColor"),WS=Mn("borderLeftColor"),JS=Mn("outline",An),e2=Mn("outlineColor"),Ds=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=Go(n.theme,"shape.borderRadius",4),o=i=>({borderRadius:xr(r,i)});return jn(n,n.borderRadius,o)}return null};Ds.propTypes={};Ds.filterProps=["borderRadius"];Bs(VS,GS,YS,IS,XS,KS,QS,FS,ZS,WS,Ds,JS,e2);const Ns=n=>{if(n.gap!==void 0&&n.gap!==null){const r=Go(n.theme,"spacing",8),o=i=>({gap:xr(r,i)});return jn(n,n.gap,o)}return null};Ns.propTypes={};Ns.filterProps=["gap"];const _s=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=Go(n.theme,"spacing",8),o=i=>({columnGap:xr(r,i)});return jn(n,n.columnGap,o)}return null};_s.propTypes={};_s.filterProps=["columnGap"];const $s=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=Go(n.theme,"spacing",8),o=i=>({rowGap:xr(r,i)});return jn(n,n.rowGap,o)}return null};$s.propTypes={};$s.filterProps=["rowGap"];const t2=Ot({prop:"gridColumn"}),n2=Ot({prop:"gridRow"}),a2=Ot({prop:"gridAutoFlow"}),r2=Ot({prop:"gridAutoColumns"}),l2=Ot({prop:"gridAutoRows"}),o2=Ot({prop:"gridTemplateColumns"}),i2=Ot({prop:"gridTemplateRows"}),s2=Ot({prop:"gridTemplateAreas"}),u2=Ot({prop:"gridArea"});Bs(Ns,_s,$s,t2,n2,a2,r2,l2,o2,i2,s2,u2);function gl(n,r){return r==="grey"?r:n}const c2=Ot({prop:"color",themeKey:"palette",transform:gl}),f2=Ot({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:gl}),d2=Ot({prop:"backgroundColor",themeKey:"palette",transform:gl});Bs(c2,f2,d2);function pn(n){return n<=1&&n!==0?`${n*100}%`:n}const p2=Ot({prop:"width",transform:pn}),ud=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=o=>{const i=n.theme?.breakpoints?.values?.[o]||ks[o];return i?n.theme?.breakpoints?.unit!=="px"?{maxWidth:`${i}${n.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:pn(o)}};return jn(n,n.maxWidth,r)}return null};ud.filterProps=["maxWidth"];const m2=Ot({prop:"minWidth",transform:pn}),h2=Ot({prop:"height",transform:pn}),g2=Ot({prop:"maxHeight",transform:pn}),y2=Ot({prop:"minHeight",transform:pn});Ot({prop:"size",cssProperty:"width",transform:pn});Ot({prop:"size",cssProperty:"height",transform:pn});const v2=Ot({prop:"boxSizing"});Bs(p2,ud,m2,h2,g2,y2,v2);const Yo={border:{themeKey:"borders",transform:An},borderTop:{themeKey:"borders",transform:An},borderRight:{themeKey:"borders",transform:An},borderBottom:{themeKey:"borders",transform:An},borderLeft:{themeKey:"borders",transform:An},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:An},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Ds},color:{themeKey:"palette",transform:gl},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:gl},backgroundColor:{themeKey:"palette",transform:gl},p:{style:Rt},pt:{style:Rt},pr:{style:Rt},pb:{style:Rt},pl:{style:Rt},px:{style:Rt},py:{style:Rt},padding:{style:Rt},paddingTop:{style:Rt},paddingRight:{style:Rt},paddingBottom:{style:Rt},paddingLeft:{style:Rt},paddingX:{style:Rt},paddingY:{style:Rt},paddingInline:{style:Rt},paddingInlineStart:{style:Rt},paddingInlineEnd:{style:Rt},paddingBlock:{style:Rt},paddingBlockStart:{style:Rt},paddingBlockEnd:{style:Rt},m:{style:Et},mt:{style:Et},mr:{style:Et},mb:{style:Et},ml:{style:Et},mx:{style:Et},my:{style:Et},margin:{style:Et},marginTop:{style:Et},marginRight:{style:Et},marginBottom:{style:Et},marginLeft:{style:Et},marginX:{style:Et},marginY:{style:Et},marginInline:{style:Et},marginInlineStart:{style:Et},marginInlineEnd:{style:Et},marginBlock:{style:Et},marginBlockStart:{style:Et},marginBlockEnd:{style:Et},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Ns},rowGap:{style:$s},columnGap:{style:_s},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:pn},maxWidth:{style:ud},minWidth:{transform:pn},height:{transform:pn},maxHeight:{transform:pn},minHeight:{transform:pn},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function b2(...n){const r=n.reduce((i,u)=>i.concat(Object.keys(u)),[]),o=new Set(r);return n.every(i=>o.size===Object.keys(i).length)}function S2(n,r){return typeof n=="function"?n(r):n}function x2(){function n(o,i,u,f){const d={[o]:i,theme:u},p=f[o];if(!p)return{[o]:i};const{cssProperty:g=o,themeKey:h,transform:v,style:x}=p;if(i==null)return null;if(h==="typography"&&i==="inherit")return{[o]:i};const R=Vn(u,h)||{};return x?x(d):jn(d,i,T=>{let E=xs(R,v,T);return T===E&&typeof T=="string"&&(E=xs(R,v,`${o}${T==="default"?"":ie(T)}`,T)),g===!1?E:{[g]:E}})}function r(o){const{sx:i,theme:u={},nested:f}=o||{};if(!i)return null;const d=u.unstable_sxConfig??Yo;function p(g){let h=g;if(typeof g=="function")h=g(u);else if(typeof g!="object")return g;if(!h)return null;const v=u0(u.breakpoints),x=Object.keys(v);let R=v;return Object.keys(h).forEach(A=>{const T=S2(h[A],u);if(T!=null)if(typeof T=="object")if(d[A])R=wo(R,n(A,T,u,d));else{const E=jn({theme:u},T,j=>({[A]:j}));b2(E,T)?R[A]=r({sx:T,theme:u,nested:!0}):R=wo(R,E)}else R=wo(R,n(A,T,u,d))}),!f&&u.modularCssLayers?{"@layer sx":Yg(u,Nf(x,R))}:Yg(u,Nf(x,R))}return Array.isArray(i)?i.map(p):p(i)}return r}const Ia=x2();Ia.filterProps=["sx"];function C2(n,r){const o=this;if(o.vars){if(!o.colorSchemes?.[n]||typeof o.getColorSchemeSelector!="function")return{};let i=o.getColorSchemeSelector(n);return i==="&"?r:((i.includes("data-")||i.includes("."))&&(i=`*:where(${i.replace(/\s*&$/,"")}) &`),{[i]:r})}return o.palette.mode===n?r:{}}function Io(n={},...r){const{breakpoints:o={},palette:i={},spacing:u,shape:f={},...d}=n,p=wS(o),g=f0(u);let h=Ut({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...i},spacing:g,shape:{...BS,...f}},d);return h=jS(h),h.applyStyles=C2,h=r.reduce((v,x)=>Ut(v,x),h),h.unstable_sxConfig={...Yo,...d?.unstable_sxConfig},h.unstable_sx=function(x){return Ia({sx:x,theme:this})},h}function T2(n){return Object.keys(n).length===0}function cd(n=null){const r=C.useContext(Po);return!r||T2(r)?n:r}const E2=Io();function Ls(n=E2){return cd(n)}function Sf(n){const r=Ga(n);return n!==r&&r.styles?(r.styles.match(/^@layer\s+[^{]*$/)||(r.styles=`@layer global{${r.styles}}`),r):n}function d0({styles:n,themeId:r,defaultTheme:o={}}){const i=Ls(o),u=r&&i[r]||i;let f=typeof n=="function"?n(u):n;return u.modularCssLayers&&(Array.isArray(f)?f=f.map(d=>Sf(typeof d=="function"?d(u):d)):f=Sf(f)),S.jsx(l0,{styles:f})}const R2=n=>{const r={systemProps:{},otherProps:{}},o=n?.theme?.unstable_sxConfig??Yo;return Object.keys(n).forEach(i=>{o[i]?r.systemProps[i]=n[i]:r.otherProps[i]=n[i]}),r};function fd(n){const{sx:r,...o}=n,{systemProps:i,otherProps:u}=R2(o);let f;return Array.isArray(r)?f=[i,...r]:typeof r=="function"?f=(...d)=>{const p=r(...d);return Gn(p)?{...i,...p}:i}:f={...i,...r},{...u,sx:f}}const Kg=n=>n,A2=()=>{let n=Kg;return{configure(r){n=r},generate(r){return n(r)},reset(){n=Kg}}},p0=A2();function m0(n){var r,o,i="";if(typeof n=="string"||typeof n=="number")i+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(o=m0(n[r]))&&(i&&(i+=" "),i+=o)}else for(o in n)n[o]&&(i&&(i+=" "),i+=o);return i}function ge(){for(var n,r,o=0,i="",u=arguments.length;o<u;o++)(n=arguments[o])&&(r=m0(n))&&(i&&(i+=" "),i+=r);return i}function M2(n={}){const{themeId:r,defaultTheme:o,defaultClassName:i="MuiBox-root",generateClassName:u}=n,f=o0("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(Ia);return C.forwardRef(function(g,h){const v=Ls(o),{className:x,component:R="div",...A}=fd(g);return S.jsx(f,{as:R,ref:h,className:ge(x,u?u(i):i),theme:r&&v[r]||v,...A})})}const O2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function we(n,r,o="Mui"){const i=O2[r];return i?`${o}-${i}`:`${p0.generate(n)}-${r}`}function De(n,r,o="Mui"){const i={};return r.forEach(u=>{i[u]=we(n,u,o)}),i}function h0(n){const{variants:r,...o}=n,i={variants:r,style:Ga(o),isProcessed:!0};return i.style===o||r&&r.forEach(u=>{typeof u.style!="function"&&(u.style=Ga(u.style))}),i}const w2=Io();function xf(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function yr(n,r){return r&&n&&typeof n=="object"&&n.styles&&!n.styles.startsWith("@layer")&&(n.styles=`@layer ${r}{${String(n.styles)}}`),n}function z2(n){return n?(r,o)=>o[n]:null}function k2(n,r,o){n.theme=B2(n.theme)?o:n.theme[r]||n.theme}function ys(n,r,o){const i=typeof r=="function"?r(n):r;if(Array.isArray(i))return i.flatMap(u=>ys(n,u,o));if(Array.isArray(i?.variants)){let u;if(i.isProcessed)u=o?yr(i.style,o):i.style;else{const{variants:f,...d}=i;u=o?yr(Ga(d),o):d}return g0(n,i.variants,[u],o)}return i?.isProcessed?o?yr(Ga(i.style),o):i.style:o?yr(Ga(i),o):i}function g0(n,r,o=[],i=void 0){let u;e:for(let f=0;f<r.length;f+=1){const d=r[f];if(typeof d.props=="function"){if(u??={...n,...n.ownerState,ownerState:n.ownerState},!d.props(u))continue}else for(const p in d.props)if(n[p]!==d.props[p]&&n.ownerState?.[p]!==d.props[p])continue e;typeof d.style=="function"?(u??={...n,...n.ownerState,ownerState:n.ownerState},o.push(i?yr(Ga(d.style(u)),i):d.style(u))):o.push(i?yr(Ga(d.style),i):d.style)}return o}function y0(n={}){const{themeId:r,defaultTheme:o=w2,rootShouldForwardProp:i=xf,slotShouldForwardProp:u=xf}=n;function f(p){k2(p,r,o)}return(p,g={})=>{RS(p,_=>_.filter(Y=>Y!==Ia));const{name:h,slot:v,skipVariantsResolver:x,skipSx:R,overridesResolver:A=z2(N2(v)),...T}=g,E=h&&h.startsWith("Mui")||v?"components":"custom",j=x!==void 0?x:v&&v!=="Root"&&v!=="root"||!1,N=R||!1;let U=xf;v==="Root"||v==="root"?U=i:v?U=u:D2(p)&&(U=void 0);const B=o0(p,{shouldForwardProp:U,label:j2(),...T}),k=_=>{if(_.__emotion_real===_)return _;if(typeof _=="function")return function(I){return ys(I,_,I.theme.modularCssLayers?E:void 0)};if(Gn(_)){const Y=h0(_);return function(F){return Y.variants?ys(F,Y,F.theme.modularCssLayers?E:void 0):F.theme.modularCssLayers?yr(Y.style,E):Y.style}}return _},M=(..._)=>{const Y=[],I=_.map(k),F=[];if(Y.push(f),h&&A&&F.push(function(Q){const G=Q.theme.components?.[h]?.styleOverrides;if(!G)return null;const w={};for(const K in G)w[K]=ys(Q,G[K],Q.theme.modularCssLayers?"theme":void 0);return A(Q,w)}),h&&!j&&F.push(function(Q){const G=Q.theme?.components?.[h]?.variants;return G?g0(Q,G,[],Q.theme.modularCssLayers?"theme":void 0):null}),N||F.push(Ia),Array.isArray(I[0])){const b=I.shift(),Q=new Array(Y.length).fill(""),P=new Array(F.length).fill("");let G;G=[...Q,...b,...P],G.raw=[...Q,...b.raw,...P],Y.unshift(G)}const J=[...Y,...I,...F],te=B(...J);return p.muiName&&(te.muiName=p.muiName),te};return B.withConfig&&(M.withConfig=B.withConfig),M}}function j2(n,r){return void 0}function B2(n){for(const r in n)return!1;return!0}function D2(n){return typeof n=="string"&&n.charCodeAt(0)>96}function N2(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const v0=y0();function _o(n,r,o=!1){const i={...r};for(const u in n)if(Object.prototype.hasOwnProperty.call(n,u)){const f=u;if(f==="components"||f==="slots")i[f]={...n[f],...i[f]};else if(f==="componentsProps"||f==="slotProps"){const d=n[f],p=r[f];if(!p)i[f]=d||{};else if(!d)i[f]=p;else{i[f]={...p};for(const g in d)if(Object.prototype.hasOwnProperty.call(d,g)){const h=g;i[f][h]=_o(d[h],p[h],o)}}}else f==="className"&&o&&r.className?i.className=ge(n?.className,r?.className):f==="style"&&o&&r.style?i.style={...n?.style,...r?.style}:i[f]===void 0&&(i[f]=n[f])}return i}function _2(n){const{theme:r,name:o,props:i}=n;return!r||!r.components||!r.components[o]||!r.components[o].defaultProps?i:_o(r.components[o].defaultProps,i)}function b0({props:n,name:r,defaultTheme:o,themeId:i}){let u=Ls(o);return i&&(u=u[i]||u),_2({theme:u,name:r,props:n})}const ma=typeof window<"u"?C.useLayoutEffect:C.useEffect;function $2(n,r=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,o))}function dd(n,r=0,o=1){return $2(n,r,o)}function L2(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let o=n.match(r);return o&&o[0].length===1&&(o=o.map(i=>i+i)),o?`rgb${o.length===4?"a":""}(${o.map((i,u)=>u<3?parseInt(i,16):Math.round(parseInt(i,16)/255*1e3)/1e3).join(", ")})`:""}function Xa(n){if(n.type)return n;if(n.charAt(0)==="#")return Xa(L2(n));const r=n.indexOf("("),o=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error(pa(9,n));let i=n.substring(r+1,n.length-1),u;if(o==="color"){if(i=i.split(" "),u=i.shift(),i.length===4&&i[3].charAt(0)==="/"&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(u))throw new Error(pa(10,u))}else i=i.split(",");return i=i.map(f=>parseFloat(f)),{type:o,values:i,colorSpace:u}}const U2=n=>{const r=Xa(n);return r.values.slice(0,3).map((o,i)=>r.type.includes("hsl")&&i!==0?`${o}%`:o).join(" ")},Ro=(n,r)=>{try{return U2(n)}catch{return n}};function Us(n){const{type:r,colorSpace:o}=n;let{values:i}=n;return r.includes("rgb")?i=i.map((u,f)=>f<3?parseInt(u,10):u):r.includes("hsl")&&(i[1]=`${i[1]}%`,i[2]=`${i[2]}%`),r.includes("color")?i=`${o} ${i.join(" ")}`:i=`${i.join(", ")}`,`${r}(${i})`}function S0(n){n=Xa(n);const{values:r}=n,o=r[0],i=r[1]/100,u=r[2]/100,f=i*Math.min(u,1-u),d=(h,v=(h+o/30)%12)=>u-f*Math.max(Math.min(v-3,9-v,1),-1);let p="rgb";const g=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",g.push(r[3])),Us({type:p,values:g})}function _f(n){n=Xa(n);let r=n.type==="hsl"||n.type==="hsla"?Xa(S0(n)).values:n.values;return r=r.map(o=>(n.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function H2(n,r){const o=_f(n),i=_f(r);return(Math.max(o,i)+.05)/(Math.min(o,i)+.05)}function $o(n,r){return n=Xa(n),r=dd(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,Us(n)}function dr(n,r,o){try{return $o(n,r)}catch{return n}}function Hs(n,r){if(n=Xa(n),r=dd(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]*=1-r;return Us(n)}function We(n,r,o){try{return Hs(n,r)}catch{return n}}function qs(n,r){if(n=Xa(n),r=dd(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let o=0;o<3;o+=1)n.values[o]+=(255-n.values[o])*r;else if(n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]+=(1-n.values[o])*r;return Us(n)}function Je(n,r,o){try{return qs(n,r)}catch{return n}}function q2(n,r=.15){return _f(n)>.5?Hs(n,r):qs(n,r)}function is(n,r,o){try{return q2(n,r)}catch{return n}}const x0=C.createContext(null);function pd(){return C.useContext(x0)}const P2=typeof Symbol=="function"&&Symbol.for,V2=P2?Symbol.for("mui.nested"):"__THEME_NESTED__";function G2(n,r){return typeof r=="function"?r(n):{...n,...r}}function Y2(n){const{children:r,theme:o}=n,i=pd(),u=C.useMemo(()=>{const f=i===null?{...o}:G2(i,o);return f!=null&&(f[V2]=i!==null),f},[o,i]);return S.jsx(x0.Provider,{value:u,children:r})}const C0=C.createContext();function I2({value:n,...r}){return S.jsx(C0.Provider,{value:n??!0,...r})}const X2=()=>C.useContext(C0)??!1,T0=C.createContext(void 0);function K2({value:n,children:r}){return S.jsx(T0.Provider,{value:n,children:r})}function Q2(n){const{theme:r,name:o,props:i}=n;if(!r||!r.components||!r.components[o])return i;const u=r.components[o];return u.defaultProps?_o(u.defaultProps,i,r.components.mergeClassNameAndStyle):!u.styleOverrides&&!u.variants?_o(u,i,r.components.mergeClassNameAndStyle):i}function F2({props:n,name:r}){const o=C.useContext(T0);return Q2({props:n,name:r,theme:{components:o}})}let Qg=0;function Z2(n){const[r,o]=C.useState(n),i=n||r;return C.useEffect(()=>{r==null&&(Qg+=1,o(`mui-${Qg}`))},[r]),i}const W2={...zf},Fg=W2.useId;function Xo(n){if(Fg!==void 0){const r=Fg();return n??r}return Z2(n)}function J2(n){const r=cd(),o=Xo()||"",{modularCssLayers:i}=n;let u="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!i||r!==null?u="":typeof i=="string"?u=i.replace(/mui(?!\.)/g,u):u=`@layer ${u};`,ma(()=>{const f=document.querySelector("head");if(!f)return;const d=f.firstChild;if(u){if(d&&d.hasAttribute?.("data-mui-layer-order")&&d.getAttribute("data-mui-layer-order")===o)return;const p=document.createElement("style");p.setAttribute("data-mui-layer-order",o),p.textContent=u,f.prepend(p)}else f.querySelector(`style[data-mui-layer-order="${o}"]`)?.remove()},[u,o]),u?S.jsx(d0,{styles:u}):null}const Zg={};function Wg(n,r,o,i=!1){return C.useMemo(()=>{const u=n&&r[n]||r;if(typeof o=="function"){const f=o(u),d=n?{...r,[n]:f}:f;return i?()=>d:d}return n?{...r,[n]:o}:{...r,...o}},[n,r,o,i])}function E0(n){const{children:r,theme:o,themeId:i}=n,u=cd(Zg),f=pd()||Zg,d=Wg(i,u,o),p=Wg(i,f,o,!0),g=(i?d[i]:d).direction==="rtl",h=J2(d);return S.jsx(Y2,{theme:p,children:S.jsx(Po.Provider,{value:d,children:S.jsx(I2,{value:g,children:S.jsxs(K2,{value:i?d[i].components:d.components,children:[h,r]})})})})}const Jg={theme:void 0};function ex(n){let r,o;return function(u){let f=r;return(f===void 0||u.theme!==o)&&(Jg.theme=u.theme,f=h0(n(Jg)),r=f,o=u.theme),f}}const md="mode",hd="color-scheme",tx="data-color-scheme";function nx(n){const{defaultMode:r="system",defaultLightColorScheme:o="light",defaultDarkColorScheme:i="dark",modeStorageKey:u=md,colorSchemeStorageKey:f=hd,attribute:d=tx,colorSchemeNode:p="document.documentElement",nonce:g}=n||{};let h="",v=d;if(d==="class"&&(v=".%s"),d==="data"&&(v="[data-%s]"),v.startsWith(".")){const R=v.substring(1);h+=`${p}.classList.remove('${R}'.replace('%s', light), '${R}'.replace('%s', dark));
      ${p}.classList.add('${R}'.replace('%s', colorScheme));`}const x=v.match(/\[([^[\]]+)\]/);if(x){const[R,A]=x[1].split("=");A||(h+=`${p}.removeAttribute('${R}'.replace('%s', light));
      ${p}.removeAttribute('${R}'.replace('%s', dark));`),h+=`
      ${p}.setAttribute('${R}'.replace('%s', colorScheme), ${A?`${A}.replace('%s', colorScheme)`:'""'});`}else h+=`${p}.setAttribute('${v}', colorScheme);`;return S.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?g:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${u}') || '${r}';
  const dark = localStorage.getItem('${f}-dark') || '${i}';
  const light = localStorage.getItem('${f}-light') || '${o}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${h}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function ax(){}const rx=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(o){if(typeof window>"u")return;if(!r)return o;let i;try{i=r.localStorage.getItem(n)}catch{}return i||o},set:o=>{if(r)try{r.localStorage.setItem(n,o)}catch{}},subscribe:o=>{if(!r)return ax;const i=u=>{const f=u.newValue;u.key===n&&o(f)};return r.addEventListener("storage",i),()=>{r.removeEventListener("storage",i)}}});function Cf(){}function ey(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function R0(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function lx(n){return R0(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function ox(n){const{defaultMode:r="light",defaultLightColorScheme:o,defaultDarkColorScheme:i,supportedColorSchemes:u=[],modeStorageKey:f=md,colorSchemeStorageKey:d=hd,storageWindow:p=typeof window>"u"?void 0:window,storageManager:g=rx,noSsr:h=!1}=n,v=u.join(","),x=u.length>1,R=C.useMemo(()=>g?.({key:f,storageWindow:p}),[g,f,p]),A=C.useMemo(()=>g?.({key:`${d}-light`,storageWindow:p}),[g,d,p]),T=C.useMemo(()=>g?.({key:`${d}-dark`,storageWindow:p}),[g,d,p]),[E,j]=C.useState(()=>{const I=R?.get(r)||r,F=A?.get(o)||o,J=T?.get(i)||i;return{mode:I,systemMode:ey(I),lightColorScheme:F,darkColorScheme:J}}),[N,U]=C.useState(h||!x);C.useEffect(()=>{U(!0)},[]);const B=lx(E),k=C.useCallback(I=>{j(F=>{if(I===F.mode)return F;const J=I??r;return R?.set(J),{...F,mode:J,systemMode:ey(J)}})},[R,r]),M=C.useCallback(I=>{I?typeof I=="string"?I&&!v.includes(I)?console.error(`\`${I}\` does not exist in \`theme.colorSchemes\`.`):j(F=>{const J={...F};return R0(F,te=>{te==="light"&&(A?.set(I),J.lightColorScheme=I),te==="dark"&&(T?.set(I),J.darkColorScheme=I)}),J}):j(F=>{const J={...F},te=I.light===null?o:I.light,b=I.dark===null?i:I.dark;return te&&(v.includes(te)?(J.lightColorScheme=te,A?.set(te)):console.error(`\`${te}\` does not exist in \`theme.colorSchemes\`.`)),b&&(v.includes(b)?(J.darkColorScheme=b,T?.set(b)):console.error(`\`${b}\` does not exist in \`theme.colorSchemes\`.`)),J}):j(F=>(A?.set(o),T?.set(i),{...F,lightColorScheme:o,darkColorScheme:i}))},[v,A,T,o,i]),_=C.useCallback(I=>{E.mode==="system"&&j(F=>{const J=I?.matches?"dark":"light";return F.systemMode===J?F:{...F,systemMode:J}})},[E.mode]),Y=C.useRef(_);return Y.current=_,C.useEffect(()=>{if(typeof window.matchMedia!="function"||!x)return;const I=(...J)=>Y.current(...J),F=window.matchMedia("(prefers-color-scheme: dark)");return F.addListener(I),I(F),()=>{F.removeListener(I)}},[x]),C.useEffect(()=>{if(x){const I=R?.subscribe(te=>{(!te||["light","dark","system"].includes(te))&&k(te||r)})||Cf,F=A?.subscribe(te=>{(!te||v.match(te))&&M({light:te})})||Cf,J=T?.subscribe(te=>{(!te||v.match(te))&&M({dark:te})})||Cf;return()=>{I(),F(),J()}}},[M,k,v,r,p,x,R,A,T]),{...E,mode:N?E.mode:void 0,systemMode:N?E.systemMode:void 0,colorScheme:N?B:void 0,setMode:k,setColorScheme:M}}const ix="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function sx(n){const{themeId:r,theme:o={},modeStorageKey:i=md,colorSchemeStorageKey:u=hd,disableTransitionOnChange:f=!1,defaultColorScheme:d,resolveTheme:p}=n,g={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},h=C.createContext(void 0),v=()=>C.useContext(h)||g,x={},R={};function A(N){const{children:U,theme:B,modeStorageKey:k=i,colorSchemeStorageKey:M=u,disableTransitionOnChange:_=f,storageManager:Y,storageWindow:I=typeof window>"u"?void 0:window,documentNode:F=typeof document>"u"?void 0:document,colorSchemeNode:J=typeof document>"u"?void 0:document.documentElement,disableNestedContext:te=!1,disableStyleSheetGeneration:b=!1,defaultMode:Q="system",forceThemeRerender:P=!1,noSsr:G}=N,w=C.useRef(!1),K=pd(),oe=C.useContext(h),ne=!!oe&&!te,O=C.useMemo(()=>B||(typeof o=="function"?o():o),[B]),X=O[r],le=X||O,{colorSchemes:re=x,components:ue=R,cssVarPrefix:ce}=le,se=Object.keys(re).filter(mt=>!!re[mt]).join(","),Se=C.useMemo(()=>se.split(","),[se]),Ce=typeof d=="string"?d:d.light,_e=typeof d=="string"?d:d.dark,xe=re[Ce]&&re[_e]?Q:re[le.defaultColorScheme]?.palette?.mode||le.palette?.mode,{mode:Me,setMode:$e,systemMode:St,lightColorScheme:Te,darkColorScheme:Ke,colorScheme:Ht,setColorScheme:Qe}=ox({supportedColorSchemes:Se,defaultLightColorScheme:Ce,defaultDarkColorScheme:_e,modeStorageKey:k,colorSchemeStorageKey:M,defaultMode:xe,storageManager:Y,storageWindow:I,noSsr:G});let pt=Me,dt=Ht;ne&&(pt=oe.mode,dt=oe.colorScheme);let yt=dt||le.defaultColorScheme;le.vars&&!P&&(yt=le.defaultColorScheme);const tt=C.useMemo(()=>{const mt=le.generateThemeVars?.()||le.vars,Ee={...le,components:ue,colorSchemes:re,cssVarPrefix:ce,vars:mt};if(typeof Ee.generateSpacing=="function"&&(Ee.spacing=Ee.generateSpacing()),yt){const nt=re[yt];nt&&typeof nt=="object"&&Object.keys(nt).forEach(qe=>{nt[qe]&&typeof nt[qe]=="object"?Ee[qe]={...Ee[qe],...nt[qe]}:Ee[qe]=nt[qe]})}return p?p(Ee):Ee},[le,yt,ue,re,ce]),pe=le.colorSchemeSelector;ma(()=>{if(dt&&J&&pe&&pe!=="media"){const mt=pe;let Ee=pe;if(mt==="class"&&(Ee=".%s"),mt==="data"&&(Ee="[data-%s]"),mt?.startsWith("data-")&&!mt.includes("%s")&&(Ee=`[${mt}="%s"]`),Ee.startsWith("."))J.classList.remove(...Se.map(nt=>Ee.substring(1).replace("%s",nt))),J.classList.add(Ee.substring(1).replace("%s",dt));else{const nt=Ee.replace("%s",dt).match(/\[([^\]]+)\]/);if(nt){const[qe,Qt]=nt[1].split("=");Qt||Se.forEach(at=>{J.removeAttribute(qe.replace(dt,at))}),J.setAttribute(qe,Qt?Qt.replace(/"|'/g,""):"")}else J.setAttribute(Ee,dt)}}},[dt,pe,J,Se]),C.useEffect(()=>{let mt;if(_&&w.current&&F){const Ee=F.createElement("style");Ee.appendChild(F.createTextNode(ix)),F.head.appendChild(Ee),window.getComputedStyle(F.body),mt=setTimeout(()=>{F.head.removeChild(Ee)},1)}return()=>{clearTimeout(mt)}},[dt,_,F]),C.useEffect(()=>(w.current=!0,()=>{w.current=!1}),[]);const rn=C.useMemo(()=>({allColorSchemes:Se,colorScheme:dt,darkColorScheme:Ke,lightColorScheme:Te,mode:pt,setColorScheme:Qe,setMode:$e,systemMode:St}),[Se,dt,Ke,Te,pt,Qe,$e,St,tt.colorSchemeSelector]);let xt=!0;(b||le.cssVariables===!1||ne&&K?.cssVarPrefix===ce)&&(xt=!1);const On=S.jsxs(C.Fragment,{children:[S.jsx(E0,{themeId:X?r:void 0,theme:tt,children:U}),xt&&S.jsx(l0,{styles:tt.generateStyleSheets?.()||[]})]});return ne?On:S.jsx(h.Provider,{value:rn,children:On})}const T=typeof d=="string"?d:d.light,E=typeof d=="string"?d:d.dark;return{CssVarsProvider:A,useColorScheme:v,getInitColorSchemeScript:N=>nx({colorSchemeStorageKey:u,defaultLightColorScheme:T,defaultDarkColorScheme:E,modeStorageKey:i,...N})}}function ux(n=""){function r(...i){if(!i.length)return"";const u=i[0];return typeof u=="string"&&!u.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${u}${r(...i.slice(1))})`:`, ${u}`}return(i,...u)=>`var(--${n?`${n}-`:""}${i}${r(...u)})`}const ty=(n,r,o,i=[])=>{let u=n;r.forEach((f,d)=>{d===r.length-1?Array.isArray(u)?u[Number(f)]=o:u&&typeof u=="object"&&(u[f]=o):u&&typeof u=="object"&&(u[f]||(u[f]=i.includes(f)?[]:{}),u=u[f])})},cx=(n,r,o)=>{function i(u,f=[],d=[]){Object.entries(u).forEach(([p,g])=>{(!o||o&&!o([...f,p]))&&g!=null&&(typeof g=="object"&&Object.keys(g).length>0?i(g,[...f,p],Array.isArray(g)?[...d,p]:d):r([...f,p],g,d))})}i(n)},fx=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(i=>n.includes(i))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function Tf(n,r){const{prefix:o,shouldSkipGeneratingVar:i}=r||{},u={},f={},d={};return cx(n,(p,g,h)=>{if((typeof g=="string"||typeof g=="number")&&(!i||!i(p,g))){const v=`--${o?`${o}-`:""}${p.join("-")}`,x=fx(p,g);Object.assign(u,{[v]:x}),ty(f,p,`var(${v})`,h),ty(d,p,`var(${v}, ${x})`,h)}},p=>p[0]==="vars"),{css:u,vars:f,varsWithDefaults:d}}function dx(n,r={}){const{getSelector:o=N,disableCssColorScheme:i,colorSchemeSelector:u,enableContrastVars:f}=r,{colorSchemes:d={},components:p,defaultColorScheme:g="light",...h}=n,{vars:v,css:x,varsWithDefaults:R}=Tf(h,r);let A=R;const T={},{[g]:E,...j}=d;if(Object.entries(j||{}).forEach(([k,M])=>{const{vars:_,css:Y,varsWithDefaults:I}=Tf(M,r);A=Ut(A,I),T[k]={css:Y,vars:_}}),E){const{css:k,vars:M,varsWithDefaults:_}=Tf(E,r);A=Ut(A,_),T[g]={css:k,vars:M}}function N(k,M){let _=u;if(u==="class"&&(_=".%s"),u==="data"&&(_="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(_=`[${u}="%s"]`),k){if(_==="media")return n.defaultColorScheme===k?":root":{[`@media (prefers-color-scheme: ${d[k]?.palette?.mode||k})`]:{":root":M}};if(_)return n.defaultColorScheme===k?`:root, ${_.replace("%s",String(k))}`:_.replace("%s",String(k))}return":root"}return{vars:A,generateThemeVars:()=>{let k={...v};return Object.entries(T).forEach(([,{vars:M}])=>{k=Ut(k,M)}),k},generateStyleSheets:()=>{const k=[],M=n.defaultColorScheme||"light";function _(F,J){Object.keys(J).length&&k.push(typeof F=="string"?{[F]:{...J}}:F)}_(o(void 0,{...x}),x);const{[M]:Y,...I}=T;if(Y){const{css:F}=Y,J=d[M]?.palette?.mode,te=!i&&J?{colorScheme:J,...F}:{...F};_(o(M,{...te}),te)}return Object.entries(I).forEach(([F,{css:J}])=>{const te=d[F]?.palette?.mode,b=!i&&te?{colorScheme:te,...J}:{...J};_(o(F,{...b}),b)}),f&&k.push({":root":{"--__l-threshold":"0.7","--__l":"clamp(0, (l / var(--__l-threshold) - 1) * -infinity, 1)","--__a":"clamp(0.87, (l / var(--__l-threshold) - 1) * -infinity, 1)"}}),k}}}function px(n){return function(o){return n==="media"?`@media (prefers-color-scheme: ${o})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${o}"] &`:n==="class"?`.${o} &`:n==="data"?`[data-${o}] &`:`${n.replace("%s",o)} &`:"&"}}function ze(n,r,o=void 0){const i={};for(const u in n){const f=n[u];let d="",p=!0;for(let g=0;g<f.length;g+=1){const h=f[g];h&&(d+=(p===!0?"":" ")+r(h),p=!1,o&&o[h]&&(d+=" "+o[h]))}i[u]=d}return i}const mx=Io(),hx=v0("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${ie(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),gx=n=>b0({props:n,name:"MuiContainer",defaultTheme:mx}),yx=(n,r)=>{const o=g=>we(r,g),{classes:i,fixed:u,disableGutters:f,maxWidth:d}=n,p={root:["root",d&&`maxWidth${ie(String(d))}`,u&&"fixed",f&&"disableGutters"]};return ze(p,o,i)};function vx(n={}){const{createStyledComponent:r=hx,useThemeProps:o=gx,componentName:i="MuiContainer"}=n,u=r(({theme:d,ownerState:p})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!p.disableGutters&&{paddingLeft:d.spacing(2),paddingRight:d.spacing(2),[d.breakpoints.up("sm")]:{paddingLeft:d.spacing(3),paddingRight:d.spacing(3)}}}),({theme:d,ownerState:p})=>p.fixed&&Object.keys(d.breakpoints.values).reduce((g,h)=>{const v=h,x=d.breakpoints.values[v];return x!==0&&(g[d.breakpoints.up(v)]={maxWidth:`${x}${d.breakpoints.unit}`}),g},{}),({theme:d,ownerState:p})=>({...p.maxWidth==="xs"&&{[d.breakpoints.up("xs")]:{maxWidth:Math.max(d.breakpoints.values.xs,444)}},...p.maxWidth&&p.maxWidth!=="xs"&&{[d.breakpoints.up(p.maxWidth)]:{maxWidth:`${d.breakpoints.values[p.maxWidth]}${d.breakpoints.unit}`}}}));return C.forwardRef(function(p,g){const h=o(p),{className:v,component:x="div",disableGutters:R=!1,fixed:A=!1,maxWidth:T="lg",classes:E,...j}=h,N={...h,component:x,disableGutters:R,fixed:A,maxWidth:T},U=yx(N,i);return S.jsx(u,{as:x,ownerState:N,className:ge(U.root,v),ref:g,...j})})}function Ef(n,r){return C.isValidElement(n)&&r.indexOf(n.type.muiName??n.type?._payload?.value?.muiName)!==-1}const bx=Io(),Sx=v0("div",{name:"MuiStack",slot:"Root"});function xx(n){return b0({props:n,name:"MuiStack",defaultTheme:bx})}function Cx(n,r){const o=C.Children.toArray(n).filter(Boolean);return o.reduce((i,u,f)=>(i.push(u),f<o.length-1&&i.push(C.cloneElement(r,{key:`separator-${f}`})),i),[])}const Tx=n=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[n],Ex=({ownerState:n,theme:r})=>{let o={display:"flex",flexDirection:"column",...jn({theme:r},bf({values:n.direction,breakpoints:r.breakpoints.values}),i=>({flexDirection:i}))};if(n.spacing){const i=js(r),u=Object.keys(r.breakpoints.values).reduce((g,h)=>((typeof n.spacing=="object"&&n.spacing[h]!=null||typeof n.direction=="object"&&n.direction[h]!=null)&&(g[h]=!0),g),{}),f=bf({values:n.direction,base:u}),d=bf({values:n.spacing,base:u});typeof f=="object"&&Object.keys(f).forEach((g,h,v)=>{if(!f[g]){const R=h>0?f[v[h-1]]:"column";f[g]=R}}),o=Ut(o,jn({theme:r},d,(g,h)=>n.useFlexGap?{gap:xr(i,g)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${Tx(h?f[h]:n.direction)}`]:xr(i,g)}}))}return o=NS(r.breakpoints,o),o};function Rx(n={}){const{createStyledComponent:r=Sx,useThemeProps:o=xx,componentName:i="MuiStack"}=n,u=()=>ze({root:["root"]},g=>we(i,g),{}),f=r(Ex);return C.forwardRef(function(g,h){const v=o(g),x=fd(v),{component:R="div",direction:A="column",spacing:T=0,divider:E,children:j,className:N,useFlexGap:U=!1,...B}=x,k={direction:A,spacing:T,useFlexGap:U},M=u();return S.jsx(f,{as:R,ownerState:k,ref:h,className:ge(M.root,N),...B,children:E?Cx(j,E):j})})}const Lo={black:"#000",white:"#fff"},Ax={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},sl={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},ul={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},xo={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},cl={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},fl={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},dl={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function A0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Lo.white,default:Lo.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const M0=A0();function O0(){return{text:{primary:Lo.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Lo.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const $f=O0();function ny(n,r,o,i){const u=i.light||i,f=i.dark||i*1.5;n[r]||(n.hasOwnProperty(o)?n[r]=n[o]:r==="light"?n.light=qs(n.main,u):r==="dark"&&(n.dark=Hs(n.main,f)))}function ay(n,r,o,i,u){const f=u.light||u,d=u.dark||u*1.5;r[o]||(r.hasOwnProperty(i)?r[o]=r[i]:o==="light"?r.light=`color-mix(in ${n}, ${r.main}, #fff ${(f*100).toFixed(0)}%)`:o==="dark"&&(r.dark=`color-mix(in ${n}, ${r.main}, #000 ${(d*100).toFixed(0)}%)`))}function Mx(n="light"){return n==="dark"?{main:cl[200],light:cl[50],dark:cl[400]}:{main:cl[700],light:cl[400],dark:cl[800]}}function Ox(n="light"){return n==="dark"?{main:sl[200],light:sl[50],dark:sl[400]}:{main:sl[500],light:sl[300],dark:sl[700]}}function wx(n="light"){return n==="dark"?{main:ul[500],light:ul[300],dark:ul[700]}:{main:ul[700],light:ul[400],dark:ul[800]}}function zx(n="light"){return n==="dark"?{main:fl[400],light:fl[300],dark:fl[700]}:{main:fl[700],light:fl[500],dark:fl[900]}}function kx(n="light"){return n==="dark"?{main:dl[400],light:dl[300],dark:dl[700]}:{main:dl[800],light:dl[500],dark:dl[900]}}function jx(n="light"){return n==="dark"?{main:xo[400],light:xo[300],dark:xo[700]}:{main:"#ed6c02",light:xo[500],dark:xo[900]}}function Bx(n){return`oklch(from ${n} var(--__l) 0 h / var(--__a))`}function gd(n){const{mode:r="light",contrastThreshold:o=3,tonalOffset:i=.2,colorSpace:u,...f}=n,d=n.primary||Mx(r),p=n.secondary||Ox(r),g=n.error||wx(r),h=n.info||zx(r),v=n.success||kx(r),x=n.warning||jx(r);function R(j){return u?Bx(j):H2(j,$f.text.primary)>=o?$f.text.primary:M0.text.primary}const A=({color:j,name:N,mainShade:U=500,lightShade:B=300,darkShade:k=700})=>{if(j={...j},!j.main&&j[U]&&(j.main=j[U]),!j.hasOwnProperty("main"))throw new Error(pa(11,N?` (${N})`:"",U));if(typeof j.main!="string")throw new Error(pa(12,N?` (${N})`:"",JSON.stringify(j.main)));return u?(ay(u,j,"light",B,i),ay(u,j,"dark",k,i)):(ny(j,"light",B,i),ny(j,"dark",k,i)),j.contrastText||(j.contrastText=R(j.main)),j};let T;return r==="light"?T=A0():r==="dark"&&(T=O0()),Ut({common:{...Lo},mode:r,primary:A({color:d,name:"primary"}),secondary:A({color:p,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:A({color:g,name:"error"}),warning:A({color:x,name:"warning"}),info:A({color:h,name:"info"}),success:A({color:v,name:"success"}),grey:Ax,contrastThreshold:o,getContrastText:R,augmentColor:A,tonalOffset:i,...T},f)}function Dx(n){const r={};return Object.entries(n).forEach(i=>{const[u,f]=i;typeof f=="object"&&(r[u]=`${f.fontStyle?`${f.fontStyle} `:""}${f.fontVariant?`${f.fontVariant} `:""}${f.fontWeight?`${f.fontWeight} `:""}${f.fontStretch?`${f.fontStretch} `:""}${f.fontSize||""}${f.lineHeight?`/${f.lineHeight} `:""}${f.fontFamily||""}`)}),r}function Nx(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function _x(n){return Math.round(n*1e5)/1e5}const ry={textTransform:"uppercase"},ly='"Roboto", "Helvetica", "Arial", sans-serif';function w0(n,r){const{fontFamily:o=ly,fontSize:i=14,fontWeightLight:u=300,fontWeightRegular:f=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:g=16,allVariants:h,pxToRem:v,...x}=typeof r=="function"?r(n):r,R=i/14,A=v||(j=>`${j/g*R}rem`),T=(j,N,U,B,k)=>({fontFamily:o,fontWeight:j,fontSize:A(N),lineHeight:U,...o===ly?{letterSpacing:`${_x(B/N)}em`}:{},...k,...h}),E={h1:T(u,96,1.167,-1.5),h2:T(u,60,1.2,-.5),h3:T(f,48,1.167,0),h4:T(f,34,1.235,.25),h5:T(f,24,1.334,0),h6:T(d,20,1.6,.15),subtitle1:T(f,16,1.75,.15),subtitle2:T(d,14,1.57,.1),body1:T(f,16,1.5,.15),body2:T(f,14,1.43,.15),button:T(d,14,1.75,.4,ry),caption:T(f,12,1.66,.4),overline:T(f,12,2.66,1,ry),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Ut({htmlFontSize:g,pxToRem:A,fontFamily:o,fontSize:i,fontWeightLight:u,fontWeightRegular:f,fontWeightMedium:d,fontWeightBold:p,...E},x,{clone:!1})}const $x=.2,Lx=.14,Ux=.12;function gt(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${$x})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${Lx})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${Ux})`].join(",")}const Hx=["none",gt(0,2,1,-1,0,1,1,0,0,1,3,0),gt(0,3,1,-2,0,2,2,0,0,1,5,0),gt(0,3,3,-2,0,3,4,0,0,1,8,0),gt(0,2,4,-1,0,4,5,0,0,1,10,0),gt(0,3,5,-1,0,5,8,0,0,1,14,0),gt(0,3,5,-1,0,6,10,0,0,1,18,0),gt(0,4,5,-2,0,7,10,1,0,2,16,1),gt(0,5,5,-3,0,8,10,1,0,3,14,2),gt(0,5,6,-3,0,9,12,1,0,3,16,2),gt(0,6,6,-3,0,10,14,1,0,4,18,3),gt(0,6,7,-4,0,11,15,1,0,4,20,3),gt(0,7,8,-4,0,12,17,2,0,5,22,4),gt(0,7,8,-4,0,13,19,2,0,5,24,4),gt(0,7,9,-4,0,14,21,2,0,5,26,4),gt(0,8,9,-5,0,15,22,2,0,6,28,5),gt(0,8,10,-5,0,16,24,2,0,6,30,5),gt(0,8,11,-5,0,17,26,2,0,6,32,5),gt(0,9,11,-5,0,18,28,2,0,7,34,6),gt(0,9,12,-6,0,19,29,2,0,7,36,6),gt(0,10,13,-6,0,20,31,3,0,8,38,7),gt(0,10,13,-6,0,21,33,3,0,8,40,7),gt(0,10,14,-6,0,22,35,3,0,8,42,7),gt(0,11,14,-7,0,23,36,3,0,9,44,8),gt(0,11,15,-7,0,24,38,3,0,9,46,8)],qx={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Px={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function oy(n){return`${Math.round(n)}ms`}function Vx(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function Gx(n){const r={...qx,...n.easing},o={...Px,...n.duration};return{getAutoHeightDuration:Vx,create:(u=["all"],f={})=>{const{duration:d=o.standard,easing:p=r.easeInOut,delay:g=0,...h}=f;return(Array.isArray(u)?u:[u]).map(v=>`${v} ${typeof d=="string"?d:oy(d)} ${p} ${typeof g=="string"?g:oy(g)}`).join(",")},...n,easing:r,duration:o}}const Yx={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Ix(n){return Gn(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function z0(n={}){const r={...n};function o(i){const u=Object.entries(i);for(let f=0;f<u.length;f++){const[d,p]=u[f];!Ix(p)||d.startsWith("unstable_")?delete i[d]:Gn(p)&&(i[d]={...p},o(i[d]))}}return o(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function iy(n){return typeof n=="number"?`${(n*100).toFixed(0)}%`:`calc((${n}) * 100%)`}const Xx=n=>{if(!Number.isNaN(+n))return+n;const r=n.match(/\d*\.?\d+/g);if(!r)return 0;let o=0;for(let i=0;i<r.length;i+=1)o+=+r[i];return o};function Kx(n){Object.assign(n,{alpha(r,o){const i=this||n;return i.colorSpace?`oklch(from ${r} l c h / ${typeof o=="string"?`calc(${o})`:o})`:i.vars?`rgba(${r.replace(/var\(--([^,\s)]+)(?:,[^)]+)?\)+/g,"var(--$1Channel)")} / ${typeof o=="string"?`calc(${o})`:o})`:$o(r,Xx(o))},lighten(r,o){const i=this||n;return i.colorSpace?`color-mix(in ${i.colorSpace}, ${r}, #fff ${iy(o)})`:qs(r,o)},darken(r,o){const i=this||n;return i.colorSpace?`color-mix(in ${i.colorSpace}, ${r}, #000 ${iy(o)})`:Hs(r,o)}})}function Lf(n={},...r){const{breakpoints:o,mixins:i={},spacing:u,palette:f={},transitions:d={},typography:p={},shape:g,colorSpace:h,...v}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(pa(20));const x=gd({...f,colorSpace:h}),R=Io(n);let A=Ut(R,{mixins:Nx(R.breakpoints,i),palette:x,shadows:Hx.slice(),typography:w0(x,p),transitions:Gx(d),zIndex:{...Yx}});return A=Ut(A,v),A=r.reduce((T,E)=>Ut(T,E),A),A.unstable_sxConfig={...Yo,...v?.unstable_sxConfig},A.unstable_sx=function(E){return Ia({sx:E,theme:this})},A.toRuntimeSource=z0,Kx(A),A}function Uf(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const Qx=[...Array(25)].map((n,r)=>{if(r===0)return"none";const o=Uf(r);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function k0(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function j0(n){return n==="dark"?Qx:[]}function Fx(n){const{palette:r={mode:"light"},opacity:o,overlays:i,colorSpace:u,...f}=n,d=gd({...r,colorSpace:u});return{palette:d,opacity:{...k0(d.mode),...o},overlays:i||j0(d.mode),...f}}function Zx(n){return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!n[1]?.match(/(mode|contrastThreshold|tonalOffset)/)}const Wx=n=>[...[...Array(25)].map((r,o)=>`--${n?`${n}-`:""}overlays-${o}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],Jx=n=>(r,o)=>{const i=n.rootSelector||":root",u=n.colorSchemeSelector;let f=u;if(u==="class"&&(f=".%s"),u==="data"&&(f="[data-%s]"),u?.startsWith("data-")&&!u.includes("%s")&&(f=`[${u}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return Wx(n.cssVarPrefix).forEach(p=>{d[p]=o[p],delete o[p]}),f==="media"?{[i]:o,"@media (prefers-color-scheme: dark)":{[i]:d}}:f?{[f.replace("%s",r)]:d,[`${i}, ${f.replace("%s",r)}`]:o}:{[i]:{...o,...d}}}if(f&&f!=="media")return`${i}, ${f.replace("%s",String(r))}`}else if(r){if(f==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[i]:o}};if(f)return f.replace("%s",String(r))}return i};function eC(n,r){r.forEach(o=>{n[o]||(n[o]={})})}function Z(n,r,o){!n[r]&&o&&(n[r]=o)}function Ao(n){return typeof n!="string"||!n.startsWith("hsl")?n:S0(n)}function fa(n,r){`${r}Channel`in n||(n[`${r}Channel`]=Ro(Ao(n[r])))}function tC(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const Un=n=>{try{return n()}catch{}},nC=(n="mui")=>ux(n);function Rf(n,r,o,i,u){if(!o)return;o=o===!0?{}:o;const f=u==="dark"?"dark":"light";if(!i){r[u]=Fx({...o,palette:{mode:f,...o?.palette},colorSpace:n});return}const{palette:d,...p}=Lf({...i,palette:{mode:f,...o?.palette},colorSpace:n});return r[u]={...o,palette:d,opacity:{...k0(f),...o?.opacity},overlays:o?.overlays||j0(f)},p}function aC(n={},...r){const{colorSchemes:o={light:!0},defaultColorScheme:i,disableCssColorScheme:u=!1,cssVarPrefix:f="mui",nativeColor:d=!1,shouldSkipGeneratingVar:p=Zx,colorSchemeSelector:g=o.light&&o.dark?"media":void 0,rootSelector:h=":root",...v}=n,x=Object.keys(o)[0],R=i||(o.light&&x!=="light"?"light":x),A=nC(f),{[R]:T,light:E,dark:j,...N}=o,U={...N};let B=T;if((R==="dark"&&!("dark"in o)||R==="light"&&!("light"in o))&&(B=!0),!B)throw new Error(pa(21,R));let k;d&&(k="oklch");const M=Rf(k,U,B,v,R);E&&!U.light&&Rf(k,U,E,void 0,"light"),j&&!U.dark&&Rf(k,U,j,void 0,"dark");let _={defaultColorScheme:R,...M,cssVarPrefix:f,colorSchemeSelector:g,rootSelector:h,getCssVar:A,colorSchemes:U,font:{...Dx(M.typography),...M.font},spacing:tC(v.spacing)};Object.keys(_.colorSchemes).forEach(te=>{const b=_.colorSchemes[te].palette,Q=G=>{const w=G.split("-"),K=w[1],oe=w[2];return A(G,b[K][oe])};b.mode==="light"&&(Z(b.common,"background","#fff"),Z(b.common,"onBackground","#000")),b.mode==="dark"&&(Z(b.common,"background","#000"),Z(b.common,"onBackground","#fff"));function P(G,w,K){if(k){let oe;return G===dr&&(oe=`transparent ${((1-K)*100).toFixed(0)}%`),G===We&&(oe=`#000 ${(K*100).toFixed(0)}%`),G===Je&&(oe=`#fff ${(K*100).toFixed(0)}%`),`color-mix(in ${k}, ${w}, ${oe})`}return G(w,K)}if(eC(b,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),b.mode==="light"){Z(b.Alert,"errorColor",P(We,b.error.light,.6)),Z(b.Alert,"infoColor",P(We,b.info.light,.6)),Z(b.Alert,"successColor",P(We,b.success.light,.6)),Z(b.Alert,"warningColor",P(We,b.warning.light,.6)),Z(b.Alert,"errorFilledBg",Q("palette-error-main")),Z(b.Alert,"infoFilledBg",Q("palette-info-main")),Z(b.Alert,"successFilledBg",Q("palette-success-main")),Z(b.Alert,"warningFilledBg",Q("palette-warning-main")),Z(b.Alert,"errorFilledColor",Un(()=>b.getContrastText(b.error.main))),Z(b.Alert,"infoFilledColor",Un(()=>b.getContrastText(b.info.main))),Z(b.Alert,"successFilledColor",Un(()=>b.getContrastText(b.success.main))),Z(b.Alert,"warningFilledColor",Un(()=>b.getContrastText(b.warning.main))),Z(b.Alert,"errorStandardBg",P(Je,b.error.light,.9)),Z(b.Alert,"infoStandardBg",P(Je,b.info.light,.9)),Z(b.Alert,"successStandardBg",P(Je,b.success.light,.9)),Z(b.Alert,"warningStandardBg",P(Je,b.warning.light,.9)),Z(b.Alert,"errorIconColor",Q("palette-error-main")),Z(b.Alert,"infoIconColor",Q("palette-info-main")),Z(b.Alert,"successIconColor",Q("palette-success-main")),Z(b.Alert,"warningIconColor",Q("palette-warning-main")),Z(b.AppBar,"defaultBg",Q("palette-grey-100")),Z(b.Avatar,"defaultBg",Q("palette-grey-400")),Z(b.Button,"inheritContainedBg",Q("palette-grey-300")),Z(b.Button,"inheritContainedHoverBg",Q("palette-grey-A100")),Z(b.Chip,"defaultBorder",Q("palette-grey-400")),Z(b.Chip,"defaultAvatarColor",Q("palette-grey-700")),Z(b.Chip,"defaultIconColor",Q("palette-grey-700")),Z(b.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),Z(b.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),Z(b.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),Z(b.LinearProgress,"primaryBg",P(Je,b.primary.main,.62)),Z(b.LinearProgress,"secondaryBg",P(Je,b.secondary.main,.62)),Z(b.LinearProgress,"errorBg",P(Je,b.error.main,.62)),Z(b.LinearProgress,"infoBg",P(Je,b.info.main,.62)),Z(b.LinearProgress,"successBg",P(Je,b.success.main,.62)),Z(b.LinearProgress,"warningBg",P(Je,b.warning.main,.62)),Z(b.Skeleton,"bg",k?P(dr,b.text.primary,.11):`rgba(${Q("palette-text-primaryChannel")} / 0.11)`),Z(b.Slider,"primaryTrack",P(Je,b.primary.main,.62)),Z(b.Slider,"secondaryTrack",P(Je,b.secondary.main,.62)),Z(b.Slider,"errorTrack",P(Je,b.error.main,.62)),Z(b.Slider,"infoTrack",P(Je,b.info.main,.62)),Z(b.Slider,"successTrack",P(Je,b.success.main,.62)),Z(b.Slider,"warningTrack",P(Je,b.warning.main,.62));const G=k?P(We,b.background.default,.6825):is(b.background.default,.8);Z(b.SnackbarContent,"bg",G),Z(b.SnackbarContent,"color",Un(()=>k?$f.text.primary:b.getContrastText(G))),Z(b.SpeedDialAction,"fabHoverBg",is(b.background.paper,.15)),Z(b.StepConnector,"border",Q("palette-grey-400")),Z(b.StepContent,"border",Q("palette-grey-400")),Z(b.Switch,"defaultColor",Q("palette-common-white")),Z(b.Switch,"defaultDisabledColor",Q("palette-grey-100")),Z(b.Switch,"primaryDisabledColor",P(Je,b.primary.main,.62)),Z(b.Switch,"secondaryDisabledColor",P(Je,b.secondary.main,.62)),Z(b.Switch,"errorDisabledColor",P(Je,b.error.main,.62)),Z(b.Switch,"infoDisabledColor",P(Je,b.info.main,.62)),Z(b.Switch,"successDisabledColor",P(Je,b.success.main,.62)),Z(b.Switch,"warningDisabledColor",P(Je,b.warning.main,.62)),Z(b.TableCell,"border",P(Je,P(dr,b.divider,1),.88)),Z(b.Tooltip,"bg",P(dr,b.grey[700],.92))}if(b.mode==="dark"){Z(b.Alert,"errorColor",P(Je,b.error.light,.6)),Z(b.Alert,"infoColor",P(Je,b.info.light,.6)),Z(b.Alert,"successColor",P(Je,b.success.light,.6)),Z(b.Alert,"warningColor",P(Je,b.warning.light,.6)),Z(b.Alert,"errorFilledBg",Q("palette-error-dark")),Z(b.Alert,"infoFilledBg",Q("palette-info-dark")),Z(b.Alert,"successFilledBg",Q("palette-success-dark")),Z(b.Alert,"warningFilledBg",Q("palette-warning-dark")),Z(b.Alert,"errorFilledColor",Un(()=>b.getContrastText(b.error.dark))),Z(b.Alert,"infoFilledColor",Un(()=>b.getContrastText(b.info.dark))),Z(b.Alert,"successFilledColor",Un(()=>b.getContrastText(b.success.dark))),Z(b.Alert,"warningFilledColor",Un(()=>b.getContrastText(b.warning.dark))),Z(b.Alert,"errorStandardBg",P(We,b.error.light,.9)),Z(b.Alert,"infoStandardBg",P(We,b.info.light,.9)),Z(b.Alert,"successStandardBg",P(We,b.success.light,.9)),Z(b.Alert,"warningStandardBg",P(We,b.warning.light,.9)),Z(b.Alert,"errorIconColor",Q("palette-error-main")),Z(b.Alert,"infoIconColor",Q("palette-info-main")),Z(b.Alert,"successIconColor",Q("palette-success-main")),Z(b.Alert,"warningIconColor",Q("palette-warning-main")),Z(b.AppBar,"defaultBg",Q("palette-grey-900")),Z(b.AppBar,"darkBg",Q("palette-background-paper")),Z(b.AppBar,"darkColor",Q("palette-text-primary")),Z(b.Avatar,"defaultBg",Q("palette-grey-600")),Z(b.Button,"inheritContainedBg",Q("palette-grey-800")),Z(b.Button,"inheritContainedHoverBg",Q("palette-grey-700")),Z(b.Chip,"defaultBorder",Q("palette-grey-700")),Z(b.Chip,"defaultAvatarColor",Q("palette-grey-300")),Z(b.Chip,"defaultIconColor",Q("palette-grey-300")),Z(b.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),Z(b.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),Z(b.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),Z(b.LinearProgress,"primaryBg",P(We,b.primary.main,.5)),Z(b.LinearProgress,"secondaryBg",P(We,b.secondary.main,.5)),Z(b.LinearProgress,"errorBg",P(We,b.error.main,.5)),Z(b.LinearProgress,"infoBg",P(We,b.info.main,.5)),Z(b.LinearProgress,"successBg",P(We,b.success.main,.5)),Z(b.LinearProgress,"warningBg",P(We,b.warning.main,.5)),Z(b.Skeleton,"bg",k?P(dr,b.text.primary,.13):`rgba(${Q("palette-text-primaryChannel")} / 0.13)`),Z(b.Slider,"primaryTrack",P(We,b.primary.main,.5)),Z(b.Slider,"secondaryTrack",P(We,b.secondary.main,.5)),Z(b.Slider,"errorTrack",P(We,b.error.main,.5)),Z(b.Slider,"infoTrack",P(We,b.info.main,.5)),Z(b.Slider,"successTrack",P(We,b.success.main,.5)),Z(b.Slider,"warningTrack",P(We,b.warning.main,.5));const G=k?P(Je,b.background.default,.985):is(b.background.default,.98);Z(b.SnackbarContent,"bg",G),Z(b.SnackbarContent,"color",Un(()=>k?M0.text.primary:b.getContrastText(G))),Z(b.SpeedDialAction,"fabHoverBg",is(b.background.paper,.15)),Z(b.StepConnector,"border",Q("palette-grey-600")),Z(b.StepContent,"border",Q("palette-grey-600")),Z(b.Switch,"defaultColor",Q("palette-grey-300")),Z(b.Switch,"defaultDisabledColor",Q("palette-grey-600")),Z(b.Switch,"primaryDisabledColor",P(We,b.primary.main,.55)),Z(b.Switch,"secondaryDisabledColor",P(We,b.secondary.main,.55)),Z(b.Switch,"errorDisabledColor",P(We,b.error.main,.55)),Z(b.Switch,"infoDisabledColor",P(We,b.info.main,.55)),Z(b.Switch,"successDisabledColor",P(We,b.success.main,.55)),Z(b.Switch,"warningDisabledColor",P(We,b.warning.main,.55)),Z(b.TableCell,"border",P(We,P(dr,b.divider,1),.68)),Z(b.Tooltip,"bg",P(dr,b.grey[700],.92))}fa(b.background,"default"),fa(b.background,"paper"),fa(b.common,"background"),fa(b.common,"onBackground"),fa(b,"divider"),Object.keys(b).forEach(G=>{const w=b[G];G!=="tonalOffset"&&w&&typeof w=="object"&&(w.main&&Z(b[G],"mainChannel",Ro(Ao(w.main))),w.light&&Z(b[G],"lightChannel",Ro(Ao(w.light))),w.dark&&Z(b[G],"darkChannel",Ro(Ao(w.dark))),w.contrastText&&Z(b[G],"contrastTextChannel",Ro(Ao(w.contrastText))),G==="text"&&(fa(b[G],"primary"),fa(b[G],"secondary")),G==="action"&&(w.active&&fa(b[G],"active"),w.selected&&fa(b[G],"selected")))})}),_=r.reduce((te,b)=>Ut(te,b),_);const Y={prefix:f,disableCssColorScheme:u,shouldSkipGeneratingVar:p,getSelector:Jx(_),enableContrastVars:d},{vars:I,generateThemeVars:F,generateStyleSheets:J}=dx(_,Y);return _.vars=I,Object.entries(_.colorSchemes[_.defaultColorScheme]).forEach(([te,b])=>{_[te]=b}),_.generateThemeVars=F,_.generateStyleSheets=J,_.generateSpacing=function(){return f0(v.spacing,js(this))},_.getColorSchemeSelector=px(g),_.spacing=_.generateSpacing(),_.shouldSkipGeneratingVar=p,_.unstable_sxConfig={...Yo,...v?.unstable_sxConfig},_.unstable_sx=function(b){return Ia({sx:b,theme:this})},_.toRuntimeSource=z0,_}function sy(n,r,o){n.colorSchemes&&o&&(n.colorSchemes[r]={...o!==!0&&o,palette:gd({...o===!0?{}:o.palette,mode:r})})}function Ps(n={},...r){const{palette:o,cssVariables:i=!1,colorSchemes:u=o?void 0:{light:!0},defaultColorScheme:f=o?.mode,...d}=n,p=f||"light",g=u?.[p],h={...u,...o?{[p]:{...typeof g!="boolean"&&g,palette:o}}:void 0};if(i===!1){if(!("colorSchemes"in n))return Lf(n,...r);let v=o;"palette"in n||h[p]&&(h[p]!==!0?v=h[p].palette:p==="dark"&&(v={mode:"dark"}));const x=Lf({...n,palette:v},...r);return x.defaultColorScheme=p,x.colorSchemes=h,x.palette.mode==="light"&&(x.colorSchemes.light={...h.light!==!0&&h.light,palette:x.palette},sy(x,"dark",h.dark)),x.palette.mode==="dark"&&(x.colorSchemes.dark={...h.dark!==!0&&h.dark,palette:x.palette},sy(x,"light",h.light)),x}return!o&&!("light"in h)&&p==="light"&&(h.light=!0),aC({...d,colorSchemes:h,defaultColorScheme:p,...typeof i!="boolean"&&i},...r)}const yd=Ps();function Vs(){const n=Ls(yd);return n[In]||n}function B0(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const hn=n=>B0(n)&&n!=="classes",fe=y0({themeId:In,defaultTheme:yd,rootShouldForwardProp:hn});function rC({theme:n,...r}){const o=In in n?n[In]:void 0;return S.jsx(E0,{...r,themeId:o?In:void 0,theme:o||n})}const ss={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:lC}=sx({themeId:In,theme:()=>Ps({cssVariables:!0}),colorSchemeStorageKey:ss.colorSchemeStorageKey,modeStorageKey:ss.modeStorageKey,defaultColorScheme:{light:ss.defaultLightColorScheme,dark:ss.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:w0(n.palette,n.typography)};return r.unstable_sx=function(i){return Ia({sx:i,theme:this})},r}}),oC=lC;function iC({theme:n,...r}){const o=C.useMemo(()=>{if(typeof n=="function")return n;const i=In in n?n[In]:n;return"colorSchemes"in i?null:"vars"in i?n:{...n,vars:null}},[n]);return o?S.jsx(rC,{theme:o,...r}):S.jsx(oC,{theme:n,...r})}function uy(...n){return n.reduce((r,o)=>o==null?r:function(...u){r.apply(this,u),o.apply(this,u)},()=>{})}function sC(n){return S.jsx(d0,{...n,defaultTheme:yd,themeId:In})}function vd(n){return function(o){return S.jsx(sC,{styles:typeof n=="function"?i=>n({theme:i,...o}):n})}}function uC(){return fd}const He=ex;function Ne(n){return F2(n)}function cC(n){return we("MuiSvgIcon",n)}De("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const fC=n=>{const{color:r,fontSize:o,classes:i}=n,u={root:["root",r!=="inherit"&&`color${ie(r)}`,`fontSize${ie(o)}`]};return ze(u,cC,i)},dC=fe("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color!=="inherit"&&r[`color${ie(o.color)}`],r[`fontSize${ie(o.fontSize)}`]]}})(He(({theme:n})=>({userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:n.transitions?.create?.("fill",{duration:(n.vars??n).transitions?.duration?.shorter}),variants:[{props:r=>!r.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:n.typography?.pxToRem?.(20)||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:n.typography?.pxToRem?.(24)||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:n.typography?.pxToRem?.(35)||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,r])=>r&&r.main).map(([r])=>({props:{color:r},style:{color:(n.vars??n).palette?.[r]?.main}})),{props:{color:"action"},style:{color:(n.vars??n).palette?.action?.active}},{props:{color:"disabled"},style:{color:(n.vars??n).palette?.action?.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}))),Hf=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiSvgIcon"}),{children:u,className:f,color:d="inherit",component:p="svg",fontSize:g="medium",htmlColor:h,inheritViewBox:v=!1,titleAccess:x,viewBox:R="0 0 24 24",...A}=i,T=C.isValidElement(u)&&u.type==="svg",E={...i,color:d,component:p,fontSize:g,instanceFontSize:r.fontSize,inheritViewBox:v,viewBox:R,hasSvgAsChild:T},j={};v||(j.viewBox=R);const N=fC(E);return S.jsxs(dC,{as:p,className:ge(N.root,f),focusable:"false",color:h,"aria-hidden":x?void 0:!0,role:x?"img":void 0,ref:o,...j,...A,...T&&u.props,ownerState:E,children:[T?u.props.children:u,x?S.jsx("title",{children:x}):null]})});Hf.muiName="SvgIcon";function ft(n,r){function o(i,u){return S.jsx(Hf,{"data-testid":void 0,ref:u,...i,children:n})}return o.muiName=Hf.muiName,C.memo(C.forwardRef(o))}function D0(n,r=166){let o;function i(...u){const f=()=>{n.apply(this,u)};clearTimeout(o),o=setTimeout(f,r)}return i.clear=()=>{clearTimeout(o)},i}function Bn(n){return n&&n.ownerDocument||document}function ha(n){return Bn(n).defaultView||window}function cy(n,r){typeof n=="function"?n(r):n&&(n.current=r)}function qf(n){const{controlled:r,default:o,name:i,state:u="value"}=n,{current:f}=C.useRef(r!==void 0),[d,p]=C.useState(o),g=f?r:d,h=C.useCallback(v=>{f||p(v)},[]);return[g,h]}function br(n){const r=C.useRef(n);return ma(()=>{r.current=n}),C.useRef((...o)=>(0,r.current)(...o)).current}function Zt(...n){const r=C.useRef(void 0),o=C.useCallback(i=>{const u=n.map(f=>{if(f==null)return null;if(typeof f=="function"){const d=f,p=d(i);return typeof p=="function"?p:()=>{d(null)}}return f.current=i,()=>{f.current=null}});return()=>{u.forEach(f=>f?.())}},n);return C.useMemo(()=>n.every(i=>i==null)?null:i=>{r.current&&(r.current(),r.current=void 0),i!=null&&(r.current=o(i))},n)}function pC(n,r){const o=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&o>=65&&o<=90&&typeof r=="function"}function N0(n,r){if(!n)return r;function o(d,p){const g={};return Object.keys(p).forEach(h=>{pC(h,p[h])&&typeof d[h]=="function"&&(g[h]=(...v)=>{d[h](...v),p[h](...v)})}),g}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,g=typeof n=="function"?n({...d,...p}):n,h=ge(d?.className,p?.className,g?.className),v=o(g,p);return{...p,...g,...v,...!!h&&{className:h},...p?.style&&g?.style&&{style:{...p.style,...g.style}},...p?.sx&&g?.sx&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(g.sx)?g.sx:[g.sx]]}}};const i=r,u=o(n,i),f=ge(i?.className,n?.className);return{...r,...n,...u,...!!f&&{className:f},...i?.style&&n?.style&&{style:{...i.style,...n.style}},...i?.sx&&n?.sx&&{sx:[...Array.isArray(i.sx)?i.sx:[i.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function _0(n,r){if(n==null)return{};var o={};for(var i in n)if({}.hasOwnProperty.call(n,i)){if(r.indexOf(i)!==-1)continue;o[i]=n[i]}return o}function Pf(n,r){return Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,i){return o.__proto__=i,o},Pf(n,r)}function $0(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,Pf(n,r)}var L0=Yy();const us=Gy(L0),fy={disabled:!1},Cs=Yn.createContext(null);var mC=function(r){return r.scrollTop},Mo="unmounted",hr="exited",gr="entering",ml="entered",Vf="exiting",Kn=(function(n){$0(r,n);function r(i,u){var f;f=n.call(this,i,u)||this;var d=u,p=d&&!d.isMounting?i.enter:i.appear,g;return f.appearStatus=null,i.in?p?(g=hr,f.appearStatus=gr):g=ml:i.unmountOnExit||i.mountOnEnter?g=Mo:g=hr,f.state={status:g},f.nextCallback=null,f}r.getDerivedStateFromProps=function(u,f){var d=u.in;return d&&f.status===Mo?{status:hr}:null};var o=r.prototype;return o.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},o.componentDidUpdate=function(u){var f=null;if(u!==this.props){var d=this.state.status;this.props.in?d!==gr&&d!==ml&&(f=gr):(d===gr||d===ml)&&(f=Vf)}this.updateStatus(!1,f)},o.componentWillUnmount=function(){this.cancelNextCallback()},o.getTimeouts=function(){var u=this.props.timeout,f,d,p;return f=d=p=u,u!=null&&typeof u!="number"&&(f=u.exit,d=u.enter,p=u.appear!==void 0?u.appear:d),{exit:f,enter:d,appear:p}},o.updateStatus=function(u,f){if(u===void 0&&(u=!1),f!==null)if(this.cancelNextCallback(),f===gr){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:us.findDOMNode(this);d&&mC(d)}this.performEnter(u)}else this.performExit();else this.props.unmountOnExit&&this.state.status===hr&&this.setState({status:Mo})},o.performEnter=function(u){var f=this,d=this.props.enter,p=this.context?this.context.isMounting:u,g=this.props.nodeRef?[p]:[us.findDOMNode(this),p],h=g[0],v=g[1],x=this.getTimeouts(),R=p?x.appear:x.enter;if(!u&&!d||fy.disabled){this.safeSetState({status:ml},function(){f.props.onEntered(h)});return}this.props.onEnter(h,v),this.safeSetState({status:gr},function(){f.props.onEntering(h,v),f.onTransitionEnd(R,function(){f.safeSetState({status:ml},function(){f.props.onEntered(h,v)})})})},o.performExit=function(){var u=this,f=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:us.findDOMNode(this);if(!f||fy.disabled){this.safeSetState({status:hr},function(){u.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:Vf},function(){u.props.onExiting(p),u.onTransitionEnd(d.exit,function(){u.safeSetState({status:hr},function(){u.props.onExited(p)})})})},o.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},o.safeSetState=function(u,f){f=this.setNextCallback(f),this.setState(u,f)},o.setNextCallback=function(u){var f=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,f.nextCallback=null,u(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},o.onTransitionEnd=function(u,f){this.setNextCallback(f);var d=this.props.nodeRef?this.props.nodeRef.current:us.findDOMNode(this),p=u==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var g=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],h=g[0],v=g[1];this.props.addEndListener(h,v)}u!=null&&setTimeout(this.nextCallback,u)},o.render=function(){var u=this.state.status;if(u===Mo)return null;var f=this.props,d=f.children;f.in,f.mountOnEnter,f.unmountOnExit,f.appear,f.enter,f.exit,f.timeout,f.addEndListener,f.onEnter,f.onEntering,f.onEntered,f.onExit,f.onExiting,f.onExited,f.nodeRef;var p=_0(f,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Yn.createElement(Cs.Provider,{value:null},typeof d=="function"?d(u,p):Yn.cloneElement(Yn.Children.only(d),p))},r})(Yn.Component);Kn.contextType=Cs;Kn.propTypes={};function pl(){}Kn.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:pl,onEntering:pl,onEntered:pl,onExit:pl,onExiting:pl,onExited:pl};Kn.UNMOUNTED=Mo;Kn.EXITED=hr;Kn.ENTERING=gr;Kn.ENTERED=ml;Kn.EXITING=Vf;function hC(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function bd(n,r){var o=function(f){return r&&C.isValidElement(f)?r(f):f},i=Object.create(null);return n&&C.Children.map(n,function(u){return u}).forEach(function(u){i[u.key]=o(u)}),i}function gC(n,r){n=n||{},r=r||{};function o(v){return v in r?r[v]:n[v]}var i=Object.create(null),u=[];for(var f in n)f in r?u.length&&(i[f]=u,u=[]):u.push(f);var d,p={};for(var g in r){if(i[g])for(d=0;d<i[g].length;d++){var h=i[g][d];p[i[g][d]]=o(h)}p[g]=o(g)}for(d=0;d<u.length;d++)p[u[d]]=o(u[d]);return p}function vr(n,r,o){return o[r]!=null?o[r]:n.props[r]}function yC(n,r){return bd(n.children,function(o){return C.cloneElement(o,{onExited:r.bind(null,o),in:!0,appear:vr(o,"appear",n),enter:vr(o,"enter",n),exit:vr(o,"exit",n)})})}function vC(n,r,o){var i=bd(n.children),u=gC(r,i);return Object.keys(u).forEach(function(f){var d=u[f];if(C.isValidElement(d)){var p=f in r,g=f in i,h=r[f],v=C.isValidElement(h)&&!h.props.in;g&&(!p||v)?u[f]=C.cloneElement(d,{onExited:o.bind(null,d),in:!0,exit:vr(d,"exit",n),enter:vr(d,"enter",n)}):!g&&p&&!v?u[f]=C.cloneElement(d,{in:!1}):g&&p&&C.isValidElement(h)&&(u[f]=C.cloneElement(d,{onExited:o.bind(null,d),in:h.props.in,exit:vr(d,"exit",n),enter:vr(d,"enter",n)}))}}),u}var bC=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},SC={component:"div",childFactory:function(r){return r}},Sd=(function(n){$0(r,n);function r(i,u){var f;f=n.call(this,i,u)||this;var d=f.handleExited.bind(hC(f));return f.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},f}var o=r.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(u,f){var d=f.children,p=f.handleExited,g=f.firstRender;return{children:g?yC(u,p):vC(u,d,p),firstRender:!1}},o.handleExited=function(u,f){var d=bd(this.props.children);u.key in d||(u.props.onExited&&u.props.onExited(f),this.mounted&&this.setState(function(p){var g=bs({},p.children);return delete g[u.key],{children:g}}))},o.render=function(){var u=this.props,f=u.component,d=u.childFactory,p=_0(u,["component","childFactory"]),g=this.state.contextValue,h=bC(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,f===null?Yn.createElement(Cs.Provider,{value:g},h):Yn.createElement(Cs.Provider,{value:g},Yn.createElement(f,p,h))},r})(Yn.Component);Sd.propTypes={};Sd.defaultProps=SC;const dy={};function U0(n,r){const o=C.useRef(dy);return o.current===dy&&(o.current=n(r)),o}const xC=[];function CC(n){C.useEffect(n,xC)}class xd{static create(){return new xd}currentId=null;start(r,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},r)}clear=()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)};disposeEffect=()=>this.clear}function H0(){const n=U0(xd.create).current;return CC(n.disposeEffect),n}const q0=n=>n.scrollTop;function Ts(n,r){const{timeout:o,easing:i,style:u={}}=n;return{duration:u.transitionDuration??(typeof o=="number"?o:o[r.mode]||0),easing:u.transitionTimingFunction??(typeof i=="object"?i[r.mode]:i),delay:u.transitionDelay}}function TC(n){return we("MuiPaper",n)}De("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const EC=n=>{const{square:r,elevation:o,variant:i,classes:u}=n,f={root:["root",i,!r&&"rounded",i==="elevation"&&`elevation${o}`]};return ze(f,TC,u)},RC=fe("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],!o.square&&r.rounded,o.variant==="elevation"&&r[`elevation${o.elevation}`]]}})(He(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),ga=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiPaper"}),u=Vs(),{className:f,component:d="div",elevation:p=1,square:g=!1,variant:h="elevation",...v}=i,x={...i,component:d,elevation:p,square:g,variant:h},R=EC(x);return S.jsx(RC,{as:d,ownerState:x,className:ge(R.root,f),ref:o,...v,style:{...h==="elevation"&&{"--Paper-shadow":(u.vars||u).shadows[p],...u.vars&&{"--Paper-overlay":u.vars.overlays?.[p]},...!u.vars&&u.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${$o("#fff",Uf(p))}, ${$o("#fff",Uf(p))})`}},...v.style}})});function Es(n){return typeof n=="string"}function P0(n,r,o){return n===void 0||Es(n)?r:{...r,ownerState:{...r.ownerState,...o}}}function V0(n,r,o){return typeof n=="function"?n(r,o):n}function G0(n,r=[]){if(n===void 0)return{};const o={};return Object.keys(n).filter(i=>i.match(/^on[A-Z]/)&&typeof n[i]=="function"&&!r.includes(i)).forEach(i=>{o[i]=n[i]}),o}function py(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(o=>!(o.match(/^on[A-Z]/)&&typeof n[o]=="function")).forEach(o=>{r[o]=n[o]}),r}function Y0(n){const{getSlotProps:r,additionalProps:o,externalSlotProps:i,externalForwardedProps:u,className:f}=n;if(!r){const A=ge(o?.className,f,u?.className,i?.className),T={...o?.style,...u?.style,...i?.style},E={...o,...u,...i};return A.length>0&&(E.className=A),Object.keys(T).length>0&&(E.style=T),{props:E,internalRef:void 0}}const d=G0({...u,...i}),p=py(i),g=py(u),h=r(d),v=ge(h?.className,o?.className,f,u?.className,i?.className),x={...h?.style,...o?.style,...u?.style,...i?.style},R={...h,...o,...g,...p};return v.length>0&&(R.className=v),Object.keys(x).length>0&&(R.style=x),{props:R,internalRef:h.ref}}function Ye(n,r){const{className:o,elementType:i,ownerState:u,externalForwardedProps:f,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...g}=r,{component:h,slots:v={[n]:void 0},slotProps:x={[n]:void 0},...R}=f,A=v[n]||i,T=V0(x[n],u),{props:{component:E,...j},internalRef:N}=Y0({className:o,...g,externalForwardedProps:n==="root"?R:void 0,externalSlotProps:T}),U=Zt(N,T?.ref,r.ref),B=n==="root"?E||h:E,k=P0(A,{...n==="root"&&!h&&!v[n]&&d,...n!=="root"&&!v[n]&&d,...j,...B&&!p&&{as:B},...B&&p&&{component:B},ref:U},u);return[A,k]}function Rs(n){try{return n.matches(":focus-visible")}catch{}return!1}class As{static create(){return new As}static use(){const r=U0(As.create).current,[o,i]=C.useState(!1);return r.shouldMount=o,r.setShouldMount=i,C.useEffect(r.mountEffect,[o]),r}constructor(){this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}mount(){return this.mounted||(this.mounted=MC(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}mountEffect=()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())};start(...r){this.mount().then(()=>this.ref.current?.start(...r))}stop(...r){this.mount().then(()=>this.ref.current?.stop(...r))}pulsate(...r){this.mount().then(()=>this.ref.current?.pulsate(...r))}}function AC(){return As.use()}function MC(){let n,r;const o=new Promise((i,u)=>{n=i,r=u});return o.resolve=n,o.reject=r,o}function OC(n){const{className:r,classes:o,pulsate:i=!1,rippleX:u,rippleY:f,rippleSize:d,in:p,onExited:g,timeout:h}=n,[v,x]=C.useState(!1),R=ge(r,o.ripple,o.rippleVisible,i&&o.ripplePulsate),A={width:d,height:d,top:-(d/2)+f,left:-(d/2)+u},T=ge(o.child,v&&o.childLeaving,i&&o.childPulsate);return!p&&!v&&x(!0),C.useEffect(()=>{if(!p&&g!=null){const E=setTimeout(g,h);return()=>{clearTimeout(E)}}},[g,p,h]),S.jsx("span",{className:R,style:A,children:S.jsx("span",{className:T})})}const Rn=De("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Gf=550,wC=80,zC=Vo`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,kC=Vo`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,jC=Vo`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,BC=fe("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),DC=fe(OC,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${Rn.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${zC};
    animation-duration: ${Gf}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${Rn.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${Rn.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${Rn.childLeaving} {
    opacity: 0;
    animation-name: ${kC};
    animation-duration: ${Gf}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${Rn.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${jC};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,NC=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiTouchRipple"}),{center:u=!1,classes:f={},className:d,...p}=i,[g,h]=C.useState([]),v=C.useRef(0),x=C.useRef(null);C.useEffect(()=>{x.current&&(x.current(),x.current=null)},[g]);const R=C.useRef(!1),A=H0(),T=C.useRef(null),E=C.useRef(null),j=C.useCallback(k=>{const{pulsate:M,rippleX:_,rippleY:Y,rippleSize:I,cb:F}=k;h(J=>[...J,S.jsx(DC,{classes:{ripple:ge(f.ripple,Rn.ripple),rippleVisible:ge(f.rippleVisible,Rn.rippleVisible),ripplePulsate:ge(f.ripplePulsate,Rn.ripplePulsate),child:ge(f.child,Rn.child),childLeaving:ge(f.childLeaving,Rn.childLeaving),childPulsate:ge(f.childPulsate,Rn.childPulsate)},timeout:Gf,pulsate:M,rippleX:_,rippleY:Y,rippleSize:I},v.current)]),v.current+=1,x.current=F},[f]),N=C.useCallback((k={},M={},_=()=>{})=>{const{pulsate:Y=!1,center:I=u||M.pulsate,fakeElement:F=!1}=M;if(k?.type==="mousedown"&&R.current){R.current=!1;return}k?.type==="touchstart"&&(R.current=!0);const J=F?null:E.current,te=J?J.getBoundingClientRect():{width:0,height:0,left:0,top:0};let b,Q,P;if(I||k===void 0||k.clientX===0&&k.clientY===0||!k.clientX&&!k.touches)b=Math.round(te.width/2),Q=Math.round(te.height/2);else{const{clientX:G,clientY:w}=k.touches&&k.touches.length>0?k.touches[0]:k;b=Math.round(G-te.left),Q=Math.round(w-te.top)}if(I)P=Math.sqrt((2*te.width**2+te.height**2)/3),P%2===0&&(P+=1);else{const G=Math.max(Math.abs((J?J.clientWidth:0)-b),b)*2+2,w=Math.max(Math.abs((J?J.clientHeight:0)-Q),Q)*2+2;P=Math.sqrt(G**2+w**2)}k?.touches?T.current===null&&(T.current=()=>{j({pulsate:Y,rippleX:b,rippleY:Q,rippleSize:P,cb:_})},A.start(wC,()=>{T.current&&(T.current(),T.current=null)})):j({pulsate:Y,rippleX:b,rippleY:Q,rippleSize:P,cb:_})},[u,j,A]),U=C.useCallback(()=>{N({},{pulsate:!0})},[N]),B=C.useCallback((k,M)=>{if(A.clear(),k?.type==="touchend"&&T.current){T.current(),T.current=null,A.start(0,()=>{B(k,M)});return}T.current=null,h(_=>_.length>0?_.slice(1):_),x.current=M},[A]);return C.useImperativeHandle(o,()=>({pulsate:U,start:N,stop:B}),[U,N,B]),S.jsx(BC,{className:ge(Rn.root,f.root,d),ref:E,...p,children:S.jsx(Sd,{component:null,exit:!0,children:g})})});function _C(n){return we("MuiButtonBase",n)}const $C=De("MuiButtonBase",["root","disabled","focusVisible"]),LC=n=>{const{disabled:r,focusVisible:o,focusVisibleClassName:i,classes:u}=n,d=ze({root:["root",r&&"disabled",o&&"focusVisible"]},_C,u);return o&&i&&(d.root+=` ${i}`),d},UC=fe("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${$C.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),Uo=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiButtonBase"}),{action:u,centerRipple:f=!1,children:d,className:p,component:g="button",disabled:h=!1,disableRipple:v=!1,disableTouchRipple:x=!1,focusRipple:R=!1,focusVisibleClassName:A,LinkComponent:T="a",onBlur:E,onClick:j,onContextMenu:N,onDragLeave:U,onFocus:B,onFocusVisible:k,onKeyDown:M,onKeyUp:_,onMouseDown:Y,onMouseLeave:I,onMouseUp:F,onTouchEnd:J,onTouchMove:te,onTouchStart:b,tabIndex:Q=0,TouchRippleProps:P,touchRippleRef:G,type:w,...K}=i,oe=C.useRef(null),ne=AC(),O=Zt(ne.ref,G),[X,le]=C.useState(!1);h&&X&&le(!1),C.useImperativeHandle(u,()=>({focusVisible:()=>{le(!0),oe.current.focus()}}),[]);const re=ne.shouldMount&&!v&&!h;C.useEffect(()=>{X&&R&&!v&&ne.pulsate()},[v,R,X,ne]);const ue=da(ne,"start",Y,x),ce=da(ne,"stop",N,x),se=da(ne,"stop",U,x),Se=da(ne,"stop",F,x),Ce=da(ne,"stop",pe=>{X&&pe.preventDefault(),I&&I(pe)},x),_e=da(ne,"start",b,x),xe=da(ne,"stop",J,x),Me=da(ne,"stop",te,x),$e=da(ne,"stop",pe=>{Rs(pe.target)||le(!1),E&&E(pe)},!1),St=br(pe=>{oe.current||(oe.current=pe.currentTarget),Rs(pe.target)&&(le(!0),k&&k(pe)),B&&B(pe)}),Te=()=>{const pe=oe.current;return g&&g!=="button"&&!(pe.tagName==="A"&&pe.href)},Ke=br(pe=>{R&&!pe.repeat&&X&&pe.key===" "&&ne.stop(pe,()=>{ne.start(pe)}),pe.target===pe.currentTarget&&Te()&&pe.key===" "&&pe.preventDefault(),M&&M(pe),pe.target===pe.currentTarget&&Te()&&pe.key==="Enter"&&!h&&(pe.preventDefault(),j&&j(pe))}),Ht=br(pe=>{R&&pe.key===" "&&X&&!pe.defaultPrevented&&ne.stop(pe,()=>{ne.pulsate(pe)}),_&&_(pe),j&&pe.target===pe.currentTarget&&Te()&&pe.key===" "&&!pe.defaultPrevented&&j(pe)});let Qe=g;Qe==="button"&&(K.href||K.to)&&(Qe=T);const pt={};Qe==="button"?(pt.type=w===void 0?"button":w,pt.disabled=h):(!K.href&&!K.to&&(pt.role="button"),h&&(pt["aria-disabled"]=h));const dt=Zt(o,oe),yt={...i,centerRipple:f,component:g,disabled:h,disableRipple:v,disableTouchRipple:x,focusRipple:R,tabIndex:Q,focusVisible:X},tt=LC(yt);return S.jsxs(UC,{as:Qe,className:ge(tt.root,p),ownerState:yt,onBlur:$e,onClick:j,onContextMenu:ce,onFocus:St,onKeyDown:Ke,onKeyUp:Ht,onMouseDown:ue,onMouseLeave:Ce,onMouseUp:Se,onDragLeave:se,onTouchEnd:xe,onTouchMove:Me,onTouchStart:_e,ref:dt,tabIndex:h?-1:Q,type:w,...pt,...K,children:[d,re?S.jsx(NC,{ref:O,center:f,...P}):null]})});function da(n,r,o,i=!1){return br(u=>(o&&o(u),i||n[r](u),!0))}function HC(n){return typeof n.main=="string"}function qC(n,r=[]){if(!HC(n))return!1;for(const o of r)if(!n.hasOwnProperty(o)||typeof n[o]!="string")return!1;return!0}function jt(n=[]){return([,r])=>r&&qC(r,n)}function PC(n){return we("MuiAlert",n)}const my=De("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);function VC(n){return we("MuiCircularProgress",n)}De("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Va=44,Yf=Vo`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,If=Vo`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,GC=typeof Yf!="string"?od`
        animation: ${Yf} 1.4s linear infinite;
      `:null,YC=typeof If!="string"?od`
        animation: ${If} 1.4s ease-in-out infinite;
      `:null,IC=n=>{const{classes:r,variant:o,color:i,disableShrink:u}=n,f={root:["root",o,`color${ie(i)}`],svg:["svg"],circle:["circle",`circle${ie(o)}`,u&&"circleDisableShrink"]};return ze(f,VC,r)},XC=fe("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`color${ie(o.color)}`]]}})(He(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:GC||{animation:`${Yf} 1.4s linear infinite`}},...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),KC=fe("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),QC=fe("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.circle,r[`circle${ie(o.variant)}`],o.disableShrink&&r.circleDisableShrink]}})(He(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:YC||{animation:`${If} 1.4s ease-in-out infinite`}}]}))),Cd=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiCircularProgress"}),{className:u,color:f="primary",disableShrink:d=!1,size:p=40,style:g,thickness:h=3.6,value:v=0,variant:x="indeterminate",...R}=i,A={...i,color:f,disableShrink:d,size:p,thickness:h,value:v,variant:x},T=IC(A),E={},j={},N={};if(x==="determinate"){const U=2*Math.PI*((Va-h)/2);E.strokeDasharray=U.toFixed(3),N["aria-valuenow"]=Math.round(v),E.strokeDashoffset=`${((100-v)/100*U).toFixed(3)}px`,j.transform="rotate(-90deg)"}return S.jsx(XC,{className:ge(T.root,u),style:{width:p,height:p,...j,...g},ownerState:A,ref:o,role:"progressbar",...N,...R,children:S.jsx(KC,{className:T.svg,ownerState:A,viewBox:`${Va/2} ${Va/2} ${Va} ${Va}`,children:S.jsx(QC,{className:T.circle,style:E,ownerState:A,cx:Va,cy:Va,r:(Va-h)/2,fill:"none",strokeWidth:h})})})});function FC(n){return we("MuiIconButton",n)}const hy=De("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),ZC=n=>{const{classes:r,disabled:o,color:i,edge:u,size:f,loading:d}=n,p={root:["root",d&&"loading",o&&"disabled",i!=="default"&&`color${ie(i)}`,u&&`edge${ie(u)}`,`size${ie(f)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return ze(p,FC,r)},WC=fe(Uo,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.loading&&r.loading,o.color!=="default"&&r[`color${ie(o.color)}`],o.edge&&r[`edge${ie(o.edge)}`],r[`size${ie(o.size)}`]]}})(He(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.alpha((n.vars||n).palette.action.active,(n.vars||n).palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),He(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.alpha((n.vars||n).palette[r].main,(n.vars||n).palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${hy.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${hy.loading}`]:{color:"transparent"}}))),JC=fe("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),I0=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiIconButton"}),{edge:u=!1,children:f,className:d,color:p="default",disabled:g=!1,disableFocusRipple:h=!1,size:v="medium",id:x,loading:R=null,loadingIndicator:A,...T}=i,E=Xo(x),j=A??S.jsx(Cd,{"aria-labelledby":E,color:"inherit",size:16}),N={...i,edge:u,color:p,disabled:g,disableFocusRipple:h,loading:R,loadingIndicator:j,size:v},U=ZC(N);return S.jsxs(WC,{id:R?E:x,className:ge(U.root,d),centerRipple:!0,focusRipple:!h,disabled:g||R,ref:o,...T,ownerState:N,children:[typeof R=="boolean"&&S.jsx("span",{className:U.loadingWrapper,style:{display:"contents"},children:S.jsx(JC,{className:U.loadingIndicator,ownerState:N,children:R&&j})}),f]})}),eT=ft(S.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),tT=ft(S.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),nT=ft(S.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),aT=ft(S.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),rT=ft(S.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),lT=n=>{const{variant:r,color:o,severity:i,classes:u}=n,f={root:["root",`color${ie(o||i)}`,`${r}${ie(o||i)}`,`${r}`],icon:["icon"],message:["message"],action:["action"]};return ze(f,PC,u)},oT=fe(ga,{name:"MuiAlert",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`${o.variant}${ie(o.color||o.severity)}`]]}})(He(({theme:n})=>{const r=n.palette.mode==="light"?n.darken:n.lighten,o=n.palette.mode==="light"?n.lighten:n.darken;return{...n.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(n.palette).filter(jt(["light"])).map(([i])=>({props:{colorSeverity:i,variant:"standard"},style:{color:n.vars?n.vars.palette.Alert[`${i}Color`]:r(n.palette[i].light,.6),backgroundColor:n.vars?n.vars.palette.Alert[`${i}StandardBg`]:o(n.palette[i].light,.9),[`& .${my.icon}`]:n.vars?{color:n.vars.palette.Alert[`${i}IconColor`]}:{color:n.palette[i].main}}})),...Object.entries(n.palette).filter(jt(["light"])).map(([i])=>({props:{colorSeverity:i,variant:"outlined"},style:{color:n.vars?n.vars.palette.Alert[`${i}Color`]:r(n.palette[i].light,.6),border:`1px solid ${(n.vars||n).palette[i].light}`,[`& .${my.icon}`]:n.vars?{color:n.vars.palette.Alert[`${i}IconColor`]}:{color:n.palette[i].main}}})),...Object.entries(n.palette).filter(jt(["dark"])).map(([i])=>({props:{colorSeverity:i,variant:"filled"},style:{fontWeight:n.typography.fontWeightMedium,...n.vars?{color:n.vars.palette.Alert[`${i}FilledColor`],backgroundColor:n.vars.palette.Alert[`${i}FilledBg`]}:{backgroundColor:n.palette.mode==="dark"?n.palette[i].dark:n.palette[i].main,color:n.palette.getContrastText(n.palette[i].main)}}}))]}})),iT=fe("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),sT=fe("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),uT=fe("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),gy={success:S.jsx(eT,{fontSize:"inherit"}),warning:S.jsx(tT,{fontSize:"inherit"}),error:S.jsx(nT,{fontSize:"inherit"}),info:S.jsx(aT,{fontSize:"inherit"})},cT=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiAlert"}),{action:u,children:f,className:d,closeText:p="Close",color:g,components:h={},componentsProps:v={},icon:x,iconMapping:R=gy,onClose:A,role:T="alert",severity:E="success",slotProps:j={},slots:N={},variant:U="standard",...B}=i,k={...i,color:g,severity:E,variant:U,colorSeverity:g||E},M=lT(k),_={slots:{closeButton:h.CloseButton,closeIcon:h.CloseIcon,...N},slotProps:{...v,...j}},[Y,I]=Ye("root",{ref:o,shouldForwardComponentProp:!0,className:ge(M.root,d),elementType:oT,externalForwardedProps:{..._,...B},ownerState:k,additionalProps:{role:T,elevation:0}}),[F,J]=Ye("icon",{className:M.icon,elementType:iT,externalForwardedProps:_,ownerState:k}),[te,b]=Ye("message",{className:M.message,elementType:sT,externalForwardedProps:_,ownerState:k}),[Q,P]=Ye("action",{className:M.action,elementType:uT,externalForwardedProps:_,ownerState:k}),[G,w]=Ye("closeButton",{elementType:I0,externalForwardedProps:_,ownerState:k}),[K,oe]=Ye("closeIcon",{elementType:rT,externalForwardedProps:_,ownerState:k});return S.jsxs(Y,{...I,children:[x!==!1?S.jsx(F,{...J,children:x||R[E]||gy[E]}):null,S.jsx(te,{...b,children:f}),u!=null?S.jsx(Q,{...P,children:u}):null,u==null&&A?S.jsx(Q,{...P,children:S.jsx(G,{size:"small","aria-label":p,title:p,color:"inherit",onClick:A,...w,children:S.jsx(K,{fontSize:"small",...oe})})}):null]})});function fT(n){return we("MuiTypography",n)}De("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const dT={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},pT=uC(),mT=n=>{const{align:r,gutterBottom:o,noWrap:i,paragraph:u,variant:f,classes:d}=n,p={root:["root",f,n.align!=="inherit"&&`align${ie(r)}`,o&&"gutterBottom",i&&"noWrap",u&&"paragraph"]};return ze(p,fT,d)},hT=fe("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.variant&&r[o.variant],o.align!=="inherit"&&r[`align${ie(o.align)}`],o.noWrap&&r.noWrap,o.gutterBottom&&r.gutterBottom,o.paragraph&&r.paragraph]}})(He(({theme:n})=>({margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([r,o])=>r!=="inherit"&&o&&typeof o=="object").map(([r,o])=>({props:{variant:r},style:o})),...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette?.text||{}).filter(([,r])=>typeof r=="string").map(([r])=>({props:{color:`text${ie(r)}`},style:{color:(n.vars||n).palette.text[r]}})),{props:({ownerState:r})=>r.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:r})=>r.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:r})=>r.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:r})=>r.paragraph,style:{marginBottom:16}}]}))),yy={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},st=C.forwardRef(function(r,o){const{color:i,...u}=Ne({props:r,name:"MuiTypography"}),f=!dT[i],d=pT({...u,...f&&{color:i}}),{align:p="inherit",className:g,component:h,gutterBottom:v=!1,noWrap:x=!1,paragraph:R=!1,variant:A="body1",variantMapping:T=yy,...E}=d,j={...d,align:p,color:i,className:g,component:h,gutterBottom:v,noWrap:x,paragraph:R,variant:A,variantMapping:T},N=h||(R?"p":T[A]||yy[A])||"span",U=mT(j);return S.jsx(hT,{as:N,ref:o,className:ge(U.root,g),...E,ownerState:j,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...E.style}})});function gT(n){return we("MuiAppBar",n)}De("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);const yT=n=>{const{color:r,position:o,classes:i}=n,u={root:["root",`color${ie(r)}`,`position${ie(o)}`]};return ze(u,gT,i)},vy=(n,r)=>n?`${n?.replace(")","")}, ${r})`:r,vT=fe(ga,{name:"MuiAppBar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`position${ie(o.position)}`],r[`color${ie(o.color)}`]]}})(He(({theme:n})=>({display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[100],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[100]),...n.applyStyles("dark",{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[900],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[900])})}},...Object.entries(n.palette).filter(jt(["contrastText"])).map(([r])=>({props:{color:r},style:{"--AppBar-background":(n.vars??n).palette[r].main,"--AppBar-color":(n.vars??n).palette[r].contrastText}})),{props:r=>r.enableColorOnDark===!0&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:r=>r.enableColorOnDark===!1&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundColor:n.vars?vy(n.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:n.vars?vy(n.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundImage:"none"})}}]}))),bT=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiAppBar"}),{className:u,color:f="primary",enableColorOnDark:d=!1,position:p="fixed",...g}=i,h={...i,color:f,position:p,enableColorOnDark:d},v=yT(h);return S.jsx(vT,{square:!0,component:"header",ownerState:h,elevation:4,className:ge(v.root,u,p==="fixed"&&"mui-fixed"),ref:o,...g})});function ST(n){const{elementType:r,externalSlotProps:o,ownerState:i,skipResolvingSlotProps:u=!1,...f}=n,d=u?{}:V0(o,i),{props:p,internalRef:g}=Y0({...f,externalSlotProps:d}),h=Zt(g,d?.ref,n.additionalProps?.ref);return P0(r,{...p,ref:h},i)}function Ko(n){return parseInt(C.version,10)>=19?n?.props?.ref||null:n?.ref||null}function xT(n){return typeof n=="function"?n():n}const CT=C.forwardRef(function(r,o){const{children:i,container:u,disablePortal:f=!1}=r,[d,p]=C.useState(null),g=Zt(C.isValidElement(i)?Ko(i):null,o);if(ma(()=>{f||p(xT(u)||document.body)},[u,f]),ma(()=>{if(d&&!f)return cy(o,d),()=>{cy(o,null)}},[o,d,f]),f){if(C.isValidElement(i)){const h={ref:g};return C.cloneElement(i,h)}return i}return d&&L0.createPortal(i,d)}),TT=ft(S.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function ET(n){return we("MuiChip",n)}const Be=De("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),RT=n=>{const{classes:r,disabled:o,size:i,color:u,iconColor:f,onDelete:d,clickable:p,variant:g}=n,h={root:["root",g,o&&"disabled",`size${ie(i)}`,`color${ie(u)}`,p&&"clickable",p&&`clickableColor${ie(u)}`,d&&"deletable",d&&`deletableColor${ie(u)}`,`${g}${ie(u)}`],label:["label",`label${ie(i)}`],avatar:["avatar",`avatar${ie(i)}`,`avatarColor${ie(u)}`],icon:["icon",`icon${ie(i)}`,`iconColor${ie(f)}`],deleteIcon:["deleteIcon",`deleteIcon${ie(i)}`,`deleteIconColor${ie(u)}`,`deleteIcon${ie(g)}Color${ie(u)}`]};return ze(h,ET,r)},AT=fe("div",{name:"MuiChip",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n,{color:i,iconColor:u,clickable:f,onDelete:d,size:p,variant:g}=o;return[{[`& .${Be.avatar}`]:r.avatar},{[`& .${Be.avatar}`]:r[`avatar${ie(p)}`]},{[`& .${Be.avatar}`]:r[`avatarColor${ie(i)}`]},{[`& .${Be.icon}`]:r.icon},{[`& .${Be.icon}`]:r[`icon${ie(p)}`]},{[`& .${Be.icon}`]:r[`iconColor${ie(u)}`]},{[`& .${Be.deleteIcon}`]:r.deleteIcon},{[`& .${Be.deleteIcon}`]:r[`deleteIcon${ie(p)}`]},{[`& .${Be.deleteIcon}`]:r[`deleteIconColor${ie(i)}`]},{[`& .${Be.deleteIcon}`]:r[`deleteIcon${ie(g)}Color${ie(i)}`]},r.root,r[`size${ie(p)}`],r[`color${ie(i)}`],f&&r.clickable,f&&i!=="default"&&r[`clickableColor${ie(i)})`],d&&r.deletable,d&&i!=="default"&&r[`deletableColor${ie(i)}`],r[g],r[`${g}${ie(i)}`]]}})(He(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[700]:n.palette.grey[300];return{maxWidth:"100%",fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,lineHeight:1.5,color:(n.vars||n).palette.text.primary,backgroundColor:(n.vars||n).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:n.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Be.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Be.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:n.vars?n.vars.palette.Chip.defaultAvatarColor:r,fontSize:n.typography.pxToRem(12)},[`& .${Be.avatarColorPrimary}`]:{color:(n.vars||n).palette.primary.contrastText,backgroundColor:(n.vars||n).palette.primary.dark},[`& .${Be.avatarColorSecondary}`]:{color:(n.vars||n).palette.secondary.contrastText,backgroundColor:(n.vars||n).palette.secondary.dark},[`& .${Be.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:n.typography.pxToRem(10)},[`& .${Be.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Be.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:n.alpha((n.vars||n).palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:n.alpha((n.vars||n).palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Be.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Be.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(n.palette).filter(jt(["contrastText"])).map(([o])=>({props:{color:o},style:{backgroundColor:(n.vars||n).palette[o].main,color:(n.vars||n).palette[o].contrastText,[`& .${Be.deleteIcon}`]:{color:n.alpha((n.vars||n).palette[o].contrastText,.7),"&:hover, &:active":{color:(n.vars||n).palette[o].contrastText}}}})),{props:o=>o.iconColor===o.color,style:{[`& .${Be.icon}`]:{color:n.vars?n.vars.palette.Chip.defaultIconColor:r}}},{props:o=>o.iconColor===o.color&&o.color!=="default",style:{[`& .${Be.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Be.focusVisible}`]:{backgroundColor:n.alpha((n.vars||n).palette.action.selected,`${(n.vars||n).palette.action.selectedOpacity} + ${(n.vars||n).palette.action.focusOpacity}`)}}},...Object.entries(n.palette).filter(jt(["dark"])).map(([o])=>({props:{color:o,onDelete:!0},style:{[`&.${Be.focusVisible}`]:{background:(n.vars||n).palette[o].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:n.alpha((n.vars||n).palette.action.selected,`${(n.vars||n).palette.action.selectedOpacity} + ${(n.vars||n).palette.action.hoverOpacity}`)},[`&.${Be.focusVisible}`]:{backgroundColor:n.alpha((n.vars||n).palette.action.selected,`${(n.vars||n).palette.action.selectedOpacity} + ${(n.vars||n).palette.action.focusOpacity}`)},"&:active":{boxShadow:(n.vars||n).shadows[1]}}},...Object.entries(n.palette).filter(jt(["dark"])).map(([o])=>({props:{color:o,clickable:!0},style:{[`&:hover, &.${Be.focusVisible}`]:{backgroundColor:(n.vars||n).palette[o].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:n.vars?`1px solid ${n.vars.palette.Chip.defaultBorder}`:`1px solid ${n.palette.mode==="light"?n.palette.grey[400]:n.palette.grey[700]}`,[`&.${Be.clickable}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Be.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`& .${Be.avatar}`]:{marginLeft:4},[`& .${Be.avatarSmall}`]:{marginLeft:2},[`& .${Be.icon}`]:{marginLeft:4},[`& .${Be.iconSmall}`]:{marginLeft:2},[`& .${Be.deleteIcon}`]:{marginRight:5},[`& .${Be.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(n.palette).filter(jt()).map(([o])=>({props:{variant:"outlined",color:o},style:{color:(n.vars||n).palette[o].main,border:`1px solid ${n.alpha((n.vars||n).palette[o].main,.7)}`,[`&.${Be.clickable}:hover`]:{backgroundColor:n.alpha((n.vars||n).palette[o].main,(n.vars||n).palette.action.hoverOpacity)},[`&.${Be.focusVisible}`]:{backgroundColor:n.alpha((n.vars||n).palette[o].main,(n.vars||n).palette.action.focusOpacity)},[`& .${Be.deleteIcon}`]:{color:n.alpha((n.vars||n).palette[o].main,.7),"&:hover, &:active":{color:(n.vars||n).palette[o].main}}}}))]}})),MT=fe("span",{name:"MuiChip",slot:"Label",overridesResolver:(n,r)=>{const{ownerState:o}=n,{size:i}=o;return[r.label,r[`label${ie(i)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function by(n){return n.key==="Backspace"||n.key==="Delete"}const X0=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiChip"}),{avatar:u,className:f,clickable:d,color:p="default",component:g,deleteIcon:h,disabled:v=!1,icon:x,label:R,onClick:A,onDelete:T,onKeyDown:E,onKeyUp:j,size:N="medium",variant:U="filled",tabIndex:B,skipFocusWhenDisabled:k=!1,slots:M={},slotProps:_={},...Y}=i,I=C.useRef(null),F=Zt(I,o),J=se=>{se.stopPropagation(),T&&T(se)},te=se=>{se.currentTarget===se.target&&by(se)&&se.preventDefault(),E&&E(se)},b=se=>{se.currentTarget===se.target&&T&&by(se)&&T(se),j&&j(se)},Q=d!==!1&&A?!0:d,P=Q||T?Uo:g||"div",G={...i,component:P,disabled:v,size:N,color:p,iconColor:C.isValidElement(x)&&x.props.color||p,onDelete:!!T,clickable:Q,variant:U},w=RT(G),K=P===Uo?{component:g||"div",focusVisibleClassName:w.focusVisible,...T&&{disableRipple:!0}}:{};let oe=null;T&&(oe=h&&C.isValidElement(h)?C.cloneElement(h,{className:ge(h.props.className,w.deleteIcon),onClick:J}):S.jsx(TT,{className:w.deleteIcon,onClick:J}));let ne=null;u&&C.isValidElement(u)&&(ne=C.cloneElement(u,{className:ge(w.avatar,u.props.className)}));let O=null;x&&C.isValidElement(x)&&(O=C.cloneElement(x,{className:ge(w.icon,x.props.className)}));const X={slots:M,slotProps:_},[le,re]=Ye("root",{elementType:AT,externalForwardedProps:{...X,...Y},ownerState:G,shouldForwardComponentProp:!0,ref:F,className:ge(w.root,f),additionalProps:{disabled:Q&&v?!0:void 0,tabIndex:k&&v?-1:B,...K},getSlotProps:se=>({...se,onClick:Se=>{se.onClick?.(Se),A?.(Se)},onKeyDown:Se=>{se.onKeyDown?.(Se),te(Se)},onKeyUp:Se=>{se.onKeyUp?.(Se),b(Se)}})}),[ue,ce]=Ye("label",{elementType:MT,externalForwardedProps:X,ownerState:G,className:w.label});return S.jsxs(le,{as:P,...re,children:[ne||O,S.jsx(ue,{...ce,children:R}),oe]})});function cs(n){return parseInt(n,10)||0}const OT={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function wT(n){for(const r in n)return!1;return!0}function Sy(n){return wT(n)||n.outerHeightStyle===0&&!n.overflowing}const zT=C.forwardRef(function(r,o){const{onChange:i,maxRows:u,minRows:f=1,style:d,value:p,...g}=r,{current:h}=C.useRef(p!=null),v=C.useRef(null),x=Zt(o,v),R=C.useRef(null),A=C.useRef(null),T=C.useCallback(()=>{const B=v.current,k=A.current;if(!B||!k)return;const _=ha(B).getComputedStyle(B);if(_.width==="0px")return{outerHeightStyle:0,overflowing:!1};k.style.width=_.width,k.value=B.value||r.placeholder||"x",k.value.slice(-1)===`
`&&(k.value+=" ");const Y=_.boxSizing,I=cs(_.paddingBottom)+cs(_.paddingTop),F=cs(_.borderBottomWidth)+cs(_.borderTopWidth),J=k.scrollHeight;k.value="x";const te=k.scrollHeight;let b=J;f&&(b=Math.max(Number(f)*te,b)),u&&(b=Math.min(Number(u)*te,b)),b=Math.max(b,te);const Q=b+(Y==="border-box"?I+F:0),P=Math.abs(b-J)<=1;return{outerHeightStyle:Q,overflowing:P}},[u,f,r.placeholder]),E=br(()=>{const B=v.current,k=T();if(!B||!k||Sy(k))return!1;const M=k.outerHeightStyle;return R.current!=null&&R.current!==M}),j=C.useCallback(()=>{const B=v.current,k=T();if(!B||!k||Sy(k))return;const M=k.outerHeightStyle;R.current!==M&&(R.current=M,B.style.height=`${M}px`),B.style.overflow=k.overflowing?"hidden":""},[T]),N=C.useRef(-1);ma(()=>{const B=D0(j),k=v?.current;if(!k)return;const M=ha(k);M.addEventListener("resize",B);let _;return typeof ResizeObserver<"u"&&(_=new ResizeObserver(()=>{E()&&(_.unobserve(k),cancelAnimationFrame(N.current),j(),N.current=requestAnimationFrame(()=>{_.observe(k)}))}),_.observe(k)),()=>{B.clear(),cancelAnimationFrame(N.current),M.removeEventListener("resize",B),_&&_.disconnect()}},[T,j,E]),ma(()=>{j()});const U=B=>{h||j();const k=B.target,M=k.value.length,_=k.value.endsWith(`
`),Y=k.selectionStart===M;_&&Y&&k.setSelectionRange(M,M),i&&i(B)};return S.jsxs(C.Fragment,{children:[S.jsx("textarea",{value:p,onChange:U,ref:x,rows:f,style:d,...g}),S.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:A,tabIndex:-1,style:{...OT.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function Cr({props:n,states:r,muiFormControl:o}){return r.reduce((i,u)=>(i[u]=n[u],o&&typeof n[u]>"u"&&(i[u]=o[u]),i),{})}const Gs=C.createContext(void 0);function ya(){return C.useContext(Gs)}function xy(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function Ms(n,r=!1){return n&&(xy(n.value)&&n.value!==""||r&&xy(n.defaultValue)&&n.defaultValue!=="")}function kT(n){return n.startAdornment}function jT(n){return we("MuiInputBase",n)}const bl=De("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var Cy;const Ys=(n,r)=>{const{ownerState:o}=n;return[r.root,o.formControl&&r.formControl,o.startAdornment&&r.adornedStart,o.endAdornment&&r.adornedEnd,o.error&&r.error,o.size==="small"&&r.sizeSmall,o.multiline&&r.multiline,o.color&&r[`color${ie(o.color)}`],o.fullWidth&&r.fullWidth,o.hiddenLabel&&r.hiddenLabel]},Is=(n,r)=>{const{ownerState:o}=n;return[r.input,o.size==="small"&&r.inputSizeSmall,o.multiline&&r.inputMultiline,o.type==="search"&&r.inputTypeSearch,o.startAdornment&&r.inputAdornedStart,o.endAdornment&&r.inputAdornedEnd,o.hiddenLabel&&r.inputHiddenLabel]},BT=n=>{const{classes:r,color:o,disabled:i,error:u,endAdornment:f,focused:d,formControl:p,fullWidth:g,hiddenLabel:h,multiline:v,readOnly:x,size:R,startAdornment:A,type:T}=n,E={root:["root",`color${ie(o)}`,i&&"disabled",u&&"error",g&&"fullWidth",d&&"focused",p&&"formControl",R&&R!=="medium"&&`size${ie(R)}`,v&&"multiline",A&&"adornedStart",f&&"adornedEnd",h&&"hiddenLabel",x&&"readOnly"],input:["input",i&&"disabled",T==="search"&&"inputTypeSearch",v&&"inputMultiline",R==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",A&&"inputAdornedStart",f&&"inputAdornedEnd",x&&"readOnly"]};return ze(E,jT,r)},Xs=fe("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Ys})(He(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${bl.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:o})=>r.multiline&&o==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),Ks=fe("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Is})(He(({theme:n})=>{const r=n.palette.mode==="light",o={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},i={opacity:"0 !important"},u=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${bl.formControl} &`]:{"&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&::-ms-input-placeholder":i,"&:focus::-webkit-input-placeholder":u,"&:focus::-moz-placeholder":u,"&:focus::-ms-input-placeholder":u},[`&.${bl.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:f})=>!f.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:f})=>f.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),Ty=vd({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),Td=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiInputBase"}),{"aria-describedby":u,autoComplete:f,autoFocus:d,className:p,color:g,components:h={},componentsProps:v={},defaultValue:x,disabled:R,disableInjectingGlobalStyles:A,endAdornment:T,error:E,fullWidth:j=!1,id:N,inputComponent:U="input",inputProps:B={},inputRef:k,margin:M,maxRows:_,minRows:Y,multiline:I=!1,name:F,onBlur:J,onChange:te,onClick:b,onFocus:Q,onKeyDown:P,onKeyUp:G,placeholder:w,readOnly:K,renderSuffix:oe,rows:ne,size:O,slotProps:X={},slots:le={},startAdornment:re,type:ue="text",value:ce,...se}=i,Se=B.value!=null?B.value:ce,{current:Ce}=C.useRef(Se!=null),_e=C.useRef(),xe=C.useCallback(at=>{},[]),Me=Zt(_e,k,B.ref,xe),[$e,St]=C.useState(!1),Te=ya(),Ke=Cr({props:i,muiFormControl:Te,states:["color","disabled","error","hiddenLabel","size","required","filled"]});Ke.focused=Te?Te.focused:$e,C.useEffect(()=>{!Te&&R&&$e&&(St(!1),J&&J())},[Te,R,$e,J]);const Ht=Te&&Te.onFilled,Qe=Te&&Te.onEmpty,pt=C.useCallback(at=>{Ms(at)?Ht&&Ht():Qe&&Qe()},[Ht,Qe]);ma(()=>{Ce&&pt({value:Se})},[Se,pt,Ce]);const dt=at=>{Q&&Q(at),B.onFocus&&B.onFocus(at),Te&&Te.onFocus?Te.onFocus(at):St(!0)},yt=at=>{J&&J(at),B.onBlur&&B.onBlur(at),Te&&Te.onBlur?Te.onBlur(at):St(!1)},tt=(at,...va)=>{if(!Ce){const Qn=at.target||_e.current;if(Qn==null)throw new Error(pa(1));pt({value:Qn.value})}B.onChange&&B.onChange(at,...va),te&&te(at,...va)};C.useEffect(()=>{pt(_e.current)},[]);const pe=at=>{_e.current&&at.currentTarget===at.target&&_e.current.focus(),b&&b(at)};let rn=U,xt=B;I&&rn==="input"&&(ne?xt={type:void 0,minRows:ne,maxRows:ne,...xt}:xt={type:void 0,maxRows:_,minRows:Y,...xt},rn=zT);const On=at=>{pt(at.animationName==="mui-auto-fill-cancel"?_e.current:{value:"x"})};C.useEffect(()=>{Te&&Te.setAdornedStart(!!re)},[Te,re]);const mt={...i,color:Ke.color||"primary",disabled:Ke.disabled,endAdornment:T,error:Ke.error,focused:Ke.focused,formControl:Te,fullWidth:j,hiddenLabel:Ke.hiddenLabel,multiline:I,size:Ke.size,startAdornment:re,type:ue},Ee=BT(mt),nt=le.root||h.Root||Xs,qe=X.root||v.root||{},Qt=le.input||h.Input||Ks;return xt={...xt,...X.input??v.input},S.jsxs(C.Fragment,{children:[!A&&typeof Ty=="function"&&(Cy||(Cy=S.jsx(Ty,{}))),S.jsxs(nt,{...qe,ref:o,onClick:pe,...se,...!Es(nt)&&{ownerState:{...mt,...qe.ownerState}},className:ge(Ee.root,qe.className,p,K&&"MuiInputBase-readOnly"),children:[re,S.jsx(Gs.Provider,{value:null,children:S.jsx(Qt,{"aria-invalid":Ke.error,"aria-describedby":u,autoComplete:f,autoFocus:d,defaultValue:x,disabled:Ke.disabled,id:N,onAnimationStart:On,name:F,placeholder:w,readOnly:K,required:Ke.required,rows:ne,value:Se,onKeyDown:P,onKeyUp:G,type:ue,...xt,...!Es(Qt)&&{as:rn,ownerState:{...mt,...xt.ownerState}},ref:Me,className:ge(Ee.input,xt.className,K&&"MuiInputBase-readOnly"),onBlur:yt,onChange:tt,onFocus:dt})}),T,oe?oe({...Ke,startAdornment:re}):null]})]})});function DT(n){return we("MuiInput",n)}const Co={...bl,...De("MuiInput",["root","underline","input"])};function NT(n){return we("MuiOutlinedInput",n)}const Hn={...bl,...De("MuiOutlinedInput",["root","notchedOutline","input"])};function _T(n){return we("MuiFilledInput",n)}const pr={...bl,...De("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},$T=ft(S.jsx("path",{d:"M7 10l5 5 5-5z"})),LT=ft(S.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}));function UT(n){return we("MuiAvatar",n)}De("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const HT=n=>{const{classes:r,variant:o,colorDefault:i}=n;return ze({root:["root",o,i&&"colorDefault"],img:["img"],fallback:["fallback"]},UT,r)},qT=fe("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],o.colorDefault&&r.colorDefault]}})(He(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(n.vars||n).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(n.vars||n).palette.background.default,...n.vars?{backgroundColor:n.vars.palette.Avatar.defaultBg}:{backgroundColor:n.palette.grey[400],...n.applyStyles("dark",{backgroundColor:n.palette.grey[600]})}}}]}))),PT=fe("img",{name:"MuiAvatar",slot:"Img"})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),VT=fe(LT,{name:"MuiAvatar",slot:"Fallback"})({width:"75%",height:"75%"});function GT({crossOrigin:n,referrerPolicy:r,src:o,srcSet:i}){const[u,f]=C.useState(!1);return C.useEffect(()=>{if(!o&&!i)return;f(!1);let d=!0;const p=new Image;return p.onload=()=>{d&&f("loaded")},p.onerror=()=>{d&&f("error")},p.crossOrigin=n,p.referrerPolicy=r,p.src=o,i&&(p.srcset=i),()=>{d=!1}},[n,r,o,i]),u}const Ya=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiAvatar"}),{alt:u,children:f,className:d,component:p="div",slots:g={},slotProps:h={},imgProps:v,sizes:x,src:R,srcSet:A,variant:T="circular",...E}=i;let j=null;const N={...i,component:p,variant:T},U=GT({...v,...typeof h.img=="function"?h.img(N):h.img,src:R,srcSet:A}),B=R||A,k=B&&U!=="error";N.colorDefault=!k,delete N.ownerState;const M=HT(N),[_,Y]=Ye("root",{ref:o,className:ge(M.root,d),elementType:qT,externalForwardedProps:{slots:g,slotProps:h,component:p,...E},ownerState:N}),[I,F]=Ye("img",{className:M.img,elementType:PT,externalForwardedProps:{slots:g,slotProps:{img:{...v,...h.img}}},additionalProps:{alt:u,src:R,srcSet:A,sizes:x},ownerState:N}),[J,te]=Ye("fallback",{className:M.fallback,elementType:VT,externalForwardedProps:{slots:g,slotProps:h},shouldForwardComponentProp:!0,ownerState:N});return k?j=S.jsx(I,{...F}):f||f===0?j=f:B&&u?j=u[0]:j=S.jsx(J,{...te}),S.jsx(_,{...Y,children:j})}),YT={entering:{opacity:1},entered:{opacity:1}},IT=C.forwardRef(function(r,o){const i=Vs(),u={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{addEndListener:f,appear:d=!0,children:p,easing:g,in:h,onEnter:v,onEntered:x,onEntering:R,onExit:A,onExited:T,onExiting:E,style:j,timeout:N=u,TransitionComponent:U=Kn,...B}=r,k=C.useRef(null),M=Zt(k,Ko(p),o),_=P=>G=>{if(P){const w=k.current;G===void 0?P(w):P(w,G)}},Y=_(R),I=_((P,G)=>{q0(P);const w=Ts({style:j,timeout:N,easing:g},{mode:"enter"});P.style.webkitTransition=i.transitions.create("opacity",w),P.style.transition=i.transitions.create("opacity",w),v&&v(P,G)}),F=_(x),J=_(E),te=_(P=>{const G=Ts({style:j,timeout:N,easing:g},{mode:"exit"});P.style.webkitTransition=i.transitions.create("opacity",G),P.style.transition=i.transitions.create("opacity",G),A&&A(P)}),b=_(T),Q=P=>{f&&f(k.current,P)};return S.jsx(U,{appear:d,in:h,nodeRef:k,onEnter:I,onEntered:F,onEntering:Y,onExit:te,onExited:b,onExiting:J,addEndListener:Q,timeout:N,...B,children:(P,{ownerState:G,...w})=>C.cloneElement(p,{style:{opacity:0,visibility:P==="exited"&&!h?"hidden":void 0,...YT[P],...j,...p.props.style},ref:M,...w})})});function XT(n){return we("MuiBackdrop",n)}De("MuiBackdrop",["root","invisible"]);const KT=n=>{const{classes:r,invisible:o}=n;return ze({root:["root",o&&"invisible"]},XT,r)},QT=fe("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),FT=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiBackdrop"}),{children:u,className:f,component:d="div",invisible:p=!1,open:g,components:h={},componentsProps:v={},slotProps:x={},slots:R={},TransitionComponent:A,transitionDuration:T,...E}=i,j={...i,component:d,invisible:p},N=KT(j),U={transition:A,root:h.Root,...R},B={...v,...x},k={component:d,slots:U,slotProps:B},[M,_]=Ye("root",{elementType:QT,externalForwardedProps:k,className:ge(N.root,f),ownerState:j}),[Y,I]=Ye("transition",{elementType:IT,externalForwardedProps:k,ownerState:j});return S.jsx(Y,{in:g,timeout:T,...E,...I,children:S.jsx(M,{"aria-hidden":!0,..._,classes:N,ref:o,children:u})})}),ZT=De("MuiBox",["root"]),WT=Ps(),et=M2({themeId:In,defaultTheme:WT,defaultClassName:ZT.root,generateClassName:p0.generate});function JT(n){return we("MuiButton",n)}const mr=De("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),eE=C.createContext({}),tE=C.createContext(void 0),nE=n=>{const{color:r,disableElevation:o,fullWidth:i,size:u,variant:f,loading:d,loadingPosition:p,classes:g}=n,h={root:["root",d&&"loading",f,`${f}${ie(r)}`,`size${ie(u)}`,`${f}Size${ie(u)}`,`color${ie(r)}`,o&&"disableElevation",i&&"fullWidth",d&&`loadingPosition${ie(p)}`],startIcon:["icon","startIcon",`iconSize${ie(u)}`],endIcon:["icon","endIcon",`iconSize${ie(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},v=ze(h,JT,g);return{...g,...v}},K0=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],aE=fe(Uo,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`${o.variant}${ie(o.color)}`],r[`size${ie(o.size)}`],r[`${o.variant}Size${ie(o.size)}`],o.color==="inherit"&&r.colorInherit,o.disableElevation&&r.disableElevation,o.fullWidth&&r.fullWidth,o.loading&&r.loading]}})(He(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],o=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${mr.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${mr.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${mr.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${mr.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(jt()).map(([i])=>({props:{color:i},style:{"--variant-textColor":(n.vars||n).palette[i].main,"--variant-outlinedColor":(n.vars||n).palette[i].main,"--variant-outlinedBorder":n.alpha((n.vars||n).palette[i].main,.5),"--variant-containedColor":(n.vars||n).palette[i].contrastText,"--variant-containedBg":(n.vars||n).palette[i].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[i].dark,"--variant-textBg":n.alpha((n.vars||n).palette[i].main,(n.vars||n).palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[i].main,"--variant-outlinedBg":n.alpha((n.vars||n).palette[i].main,(n.vars||n).palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:o,"--variant-textBg":n.alpha((n.vars||n).palette.text.primary,(n.vars||n).palette.action.hoverOpacity),"--variant-outlinedBg":n.alpha((n.vars||n).palette.text.primary,(n.vars||n).palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${mr.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${mr.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${mr.loading}`]:{color:"transparent"}}}]}})),rE=fe("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.startIcon,o.loading&&r.startIconLoadingStart,r[`iconSize${ie(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...K0]})),lE=fe("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.endIcon,o.loading&&r.endIconLoadingEnd,r[`iconSize${ie(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...K0]})),oE=fe("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),Ey=fe("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),iE=C.forwardRef(function(r,o){const i=C.useContext(eE),u=C.useContext(tE),f=_o(i,r),d=Ne({props:f,name:"MuiButton"}),{children:p,color:g="primary",component:h="button",className:v,disabled:x=!1,disableElevation:R=!1,disableFocusRipple:A=!1,endIcon:T,focusVisibleClassName:E,fullWidth:j=!1,id:N,loading:U=null,loadingIndicator:B,loadingPosition:k="center",size:M="medium",startIcon:_,type:Y,variant:I="text",...F}=d,J=Xo(N),te=B??S.jsx(Cd,{"aria-labelledby":J,color:"inherit",size:16}),b={...d,color:g,component:h,disabled:x,disableElevation:R,disableFocusRipple:A,fullWidth:j,loading:U,loadingIndicator:te,loadingPosition:k,size:M,type:Y,variant:I},Q=nE(b),P=(_||U&&k==="start")&&S.jsx(rE,{className:Q.startIcon,ownerState:b,children:_||S.jsx(Ey,{className:Q.loadingIconPlaceholder,ownerState:b})}),G=(T||U&&k==="end")&&S.jsx(lE,{className:Q.endIcon,ownerState:b,children:T||S.jsx(Ey,{className:Q.loadingIconPlaceholder,ownerState:b})}),w=u||"",K=typeof U=="boolean"?S.jsx("span",{className:Q.loadingWrapper,style:{display:"contents"},children:U&&S.jsx(oE,{className:Q.loadingIndicator,ownerState:b,children:te})}):null;return S.jsxs(aE,{ownerState:b,className:ge(i.className,Q.root,v,w),component:h,disabled:x||U,focusRipple:!A,focusVisibleClassName:ge(Q.focusVisible,E),ref:o,type:Y,id:U?J:N,...F,classes:Q,children:[P,k!=="end"&&K,p,k==="end"&&K,G]})});function sE(n){return we("MuiCard",n)}De("MuiCard",["root"]);const uE=n=>{const{classes:r}=n;return ze({root:["root"]},sE,r)},cE=fe(ga,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),Xf=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiCard"}),{className:u,raised:f=!1,...d}=i,p={...i,raised:f},g=uE(p);return S.jsx(cE,{className:ge(g.root,u),elevation:f?8:void 0,ref:o,ownerState:p,...d})});function fE(n){return we("MuiCardContent",n)}De("MuiCardContent",["root"]);const dE=n=>{const{classes:r}=n;return ze({root:["root"]},fE,r)},pE=fe("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),Kf=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiCardContent"}),{className:u,component:f="div",...d}=i,p={...i,component:f},g=dE(p);return S.jsx(pE,{as:f,className:ge(g.root,u),ownerState:p,ref:o,...d})});function mE(n){return we("PrivateSwitchBase",n)}De("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const hE=n=>{const{classes:r,checked:o,disabled:i,edge:u}=n,f={root:["root",o&&"checked",i&&"disabled",u&&`edge${ie(u)}`],input:["input"]};return ze(f,mE,r)},gE=fe(Uo,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:n,ownerState:r})=>n==="start"&&r.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:n,ownerState:r})=>n==="end"&&r.size!=="small",style:{marginRight:-12}}]}),yE=fe("input",{name:"MuiSwitchBase",shouldForwardProp:hn})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),vE=C.forwardRef(function(r,o){const{autoFocus:i,checked:u,checkedIcon:f,defaultChecked:d,disabled:p,disableFocusRipple:g=!1,edge:h=!1,icon:v,id:x,inputProps:R,inputRef:A,name:T,onBlur:E,onChange:j,onFocus:N,readOnly:U,required:B=!1,tabIndex:k,type:M,value:_,slots:Y={},slotProps:I={},...F}=r,[J,te]=qf({controlled:u,default:!!d,name:"SwitchBase",state:"checked"}),b=ya(),Q=ce=>{N&&N(ce),b&&b.onFocus&&b.onFocus(ce)},P=ce=>{E&&E(ce),b&&b.onBlur&&b.onBlur(ce)},G=ce=>{if(ce.nativeEvent.defaultPrevented)return;const se=ce.target.checked;te(se),j&&j(ce,se)};let w=p;b&&typeof w>"u"&&(w=b.disabled);const K=M==="checkbox"||M==="radio",oe={...r,checked:J,disabled:w,disableFocusRipple:g,edge:h},ne=hE(oe),O={slots:Y,slotProps:{input:R,...I}},[X,le]=Ye("root",{ref:o,elementType:gE,className:ne.root,shouldForwardComponentProp:!0,externalForwardedProps:{...O,component:"span",...F},getSlotProps:ce=>({...ce,onFocus:se=>{ce.onFocus?.(se),Q(se)},onBlur:se=>{ce.onBlur?.(se),P(se)}}),ownerState:oe,additionalProps:{centerRipple:!0,focusRipple:!g,disabled:w,role:void 0,tabIndex:null}}),[re,ue]=Ye("input",{ref:A,elementType:yE,className:ne.input,externalForwardedProps:O,getSlotProps:ce=>({...ce,onChange:se=>{ce.onChange?.(se),G(se)}}),ownerState:oe,additionalProps:{autoFocus:i,checked:u,defaultChecked:d,disabled:w,id:K?x:void 0,name:T,readOnly:U,required:B,tabIndex:k,type:M,...M==="checkbox"&&_===void 0?{}:{value:_}}});return S.jsxs(X,{...le,children:[S.jsx(re,{...ue}),J?f:v]})}),bE=ft(S.jsx("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"})),SE=ft(S.jsx("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})),xE=ft(S.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}));function CE(n){return we("MuiCheckbox",n)}const Af=De("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]),TE=n=>{const{classes:r,indeterminate:o,color:i,size:u}=n,f={root:["root",o&&"indeterminate",`color${ie(i)}`,`size${ie(u)}`]},d=ze(f,CE,r);return{...r,...d}},EE=fe(vE,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiCheckbox",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.indeterminate&&r.indeterminate,r[`size${ie(o.size)}`],o.color!=="default"&&r[`color${ie(o.color)}`]]}})(He(({theme:n})=>({color:(n.vars||n).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:n.alpha((n.vars||n).palette.action.active,(n.vars||n).palette.action.hoverOpacity)}}},...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r,disableRipple:!1},style:{"&:hover":{backgroundColor:n.alpha((n.vars||n).palette[r].main,(n.vars||n).palette.action.hoverOpacity)}}})),...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{[`&.${Af.checked}, &.${Af.indeterminate}`]:{color:(n.vars||n).palette[r].main},[`&.${Af.disabled}`]:{color:(n.vars||n).palette.action.disabled}}})),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}))),RE=S.jsx(SE,{}),AE=S.jsx(bE,{}),ME=S.jsx(xE,{}),OE=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiCheckbox"}),{checkedIcon:u=RE,color:f="primary",icon:d=AE,indeterminate:p=!1,indeterminateIcon:g=ME,inputProps:h,size:v="medium",disableRipple:x=!1,className:R,slots:A={},slotProps:T={},...E}=i,j=p?g:d,N=p?g:u,U={...i,disableRipple:x,color:f,indeterminate:p,size:v},B=TE(U),k=T.input??h,[M,_]=Ye("root",{ref:o,elementType:EE,className:ge(B.root,R),shouldForwardComponentProp:!0,externalForwardedProps:{slots:A,slotProps:T,...E},ownerState:U,additionalProps:{type:"checkbox",icon:C.cloneElement(j,{fontSize:j.props.fontSize??v}),checkedIcon:C.cloneElement(N,{fontSize:N.props.fontSize??v}),disableRipple:x,slots:A,slotProps:{input:N0(typeof k=="function"?k(U):k,{"data-indeterminate":p})}}});return S.jsx(M,{..._,classes:B})}),Qo=vx({createStyledComponent:fe("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${ie(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),useThemeProps:n=>Ne({props:n,name:"MuiContainer"})}),Qf=typeof vd({})=="function",wE=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),zE=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),Q0=(n,r=!1)=>{const o={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([f,d])=>{const p=n.getColorSchemeSelector(f);p.startsWith("@")?o[p]={":root":{colorScheme:d.palette?.mode}}:o[p.replace(/\s*&/,"")]={colorScheme:d.palette?.mode}});let i={html:wE(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...zE(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...o};const u=n.components?.MuiCssBaseline?.styleOverrides;return u&&(i=[i,u]),i},vs="mui-ecs",kE=n=>{const r=Q0(n,!1),o=Array.isArray(r)?r[0]:r;return!n.vars&&o&&(o.html[`:root:has(${vs})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([i,u])=>{const f=n.getColorSchemeSelector(i);f.startsWith("@")?o[f]={[`:root:not(:has(.${vs}))`]:{colorScheme:u.palette?.mode}}:o[f.replace(/\s*&/,"")]={[`&:not(:has(.${vs}))`]:{colorScheme:u.palette?.mode}}}),r},jE=vd(Qf?({theme:n,enableColorScheme:r})=>Q0(n,r):({theme:n})=>kE(n));function BE(n){const r=Ne({props:n,name:"MuiCssBaseline"}),{children:o,enableColorScheme:i=!1}=r;return S.jsxs(C.Fragment,{children:[Qf&&S.jsx(jE,{enableColorScheme:i}),!Qf&&!i&&S.jsx("span",{className:vs,style:{display:"none"}}),o]})}function F0(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function DE(n){const r=Bn(n);return r.body===n?ha(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function zo(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function Ry(n){return parseInt(ha(n).getComputedStyle(n).paddingRight,10)||0}function NE(n){const o=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),i=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return o||i}function Ay(n,r,o,i,u){const f=[r,o,...i];[].forEach.call(n.children,d=>{const p=!f.includes(d),g=!NE(d);p&&g&&zo(d,u)})}function Mf(n,r){let o=-1;return n.some((i,u)=>r(i)?(o=u,!0):!1),o}function _E(n,r){const o=[],i=n.container;if(!r.disableScrollLock){if(DE(i)){const d=F0(ha(i));o.push({value:i.style.paddingRight,property:"padding-right",el:i}),i.style.paddingRight=`${Ry(i)+d}px`;const p=Bn(i).querySelectorAll(".mui-fixed");[].forEach.call(p,g=>{o.push({value:g.style.paddingRight,property:"padding-right",el:g}),g.style.paddingRight=`${Ry(g)+d}px`})}let f;if(i.parentNode instanceof DocumentFragment)f=Bn(i).body;else{const d=i.parentElement,p=ha(i);f=d?.nodeName==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:i}o.push({value:f.style.overflow,property:"overflow",el:f},{value:f.style.overflowX,property:"overflow-x",el:f},{value:f.style.overflowY,property:"overflow-y",el:f}),f.style.overflow="hidden"}return()=>{o.forEach(({value:f,el:d,property:p})=>{f?d.style.setProperty(p,f):d.style.removeProperty(p)})}}function $E(n){const r=[];return[].forEach.call(n.children,o=>{o.getAttribute("aria-hidden")==="true"&&r.push(o)}),r}class LE{constructor(){this.modals=[],this.containers=[]}add(r,o){let i=this.modals.indexOf(r);if(i!==-1)return i;i=this.modals.length,this.modals.push(r),r.modalRef&&zo(r.modalRef,!1);const u=$E(o);Ay(o,r.mount,r.modalRef,u,!0);const f=Mf(this.containers,d=>d.container===o);return f!==-1?(this.containers[f].modals.push(r),i):(this.containers.push({modals:[r],container:o,restore:null,hiddenSiblings:u}),i)}mount(r,o){const i=Mf(this.containers,f=>f.modals.includes(r)),u=this.containers[i];u.restore||(u.restore=_E(u,o))}remove(r,o=!0){const i=this.modals.indexOf(r);if(i===-1)return i;const u=Mf(this.containers,d=>d.modals.includes(r)),f=this.containers[u];if(f.modals.splice(f.modals.indexOf(r),1),this.modals.splice(i,1),f.modals.length===0)f.restore&&f.restore(),r.modalRef&&zo(r.modalRef,o),Ay(f.container,r.mount,r.modalRef,f.hiddenSiblings,!1),this.containers.splice(u,1);else{const d=f.modals[f.modals.length-1];d.modalRef&&zo(d.modalRef,!1)}return i}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const UE=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function HE(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function qE(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=i=>n.ownerDocument.querySelector(`input[type="radio"]${i}`);let o=r(`[name="${n.name}"]:checked`);return o||(o=r(`[name="${n.name}"]`)),o!==n}function PE(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||qE(n))}function VE(n){const r=[],o=[];return Array.from(n.querySelectorAll(UE)).forEach((i,u)=>{const f=HE(i);f===-1||!PE(i)||(f===0?r.push(i):o.push({documentOrder:u,tabIndex:f,node:i}))}),o.sort((i,u)=>i.tabIndex===u.tabIndex?i.documentOrder-u.documentOrder:i.tabIndex-u.tabIndex).map(i=>i.node).concat(r)}function GE(){return!0}function YE(n){const{children:r,disableAutoFocus:o=!1,disableEnforceFocus:i=!1,disableRestoreFocus:u=!1,getTabbable:f=VE,isEnabled:d=GE,open:p}=n,g=C.useRef(!1),h=C.useRef(null),v=C.useRef(null),x=C.useRef(null),R=C.useRef(null),A=C.useRef(!1),T=C.useRef(null),E=Zt(Ko(r),T),j=C.useRef(null);C.useEffect(()=>{!p||!T.current||(A.current=!o)},[o,p]),C.useEffect(()=>{if(!p||!T.current)return;const B=Bn(T.current);return T.current.contains(B.activeElement)||(T.current.hasAttribute("tabIndex")||T.current.setAttribute("tabIndex","-1"),A.current&&T.current.focus()),()=>{u||(x.current&&x.current.focus&&(g.current=!0,x.current.focus()),x.current=null)}},[p]),C.useEffect(()=>{if(!p||!T.current)return;const B=Bn(T.current),k=Y=>{j.current=Y,!(i||!d()||Y.key!=="Tab")&&B.activeElement===T.current&&Y.shiftKey&&(g.current=!0,v.current&&v.current.focus())},M=()=>{const Y=T.current;if(Y===null)return;if(!B.hasFocus()||!d()||g.current){g.current=!1;return}if(Y.contains(B.activeElement)||i&&B.activeElement!==h.current&&B.activeElement!==v.current)return;if(B.activeElement!==R.current)R.current=null;else if(R.current!==null)return;if(!A.current)return;let I=[];if((B.activeElement===h.current||B.activeElement===v.current)&&(I=f(T.current)),I.length>0){const F=!!(j.current?.shiftKey&&j.current?.key==="Tab"),J=I[0],te=I[I.length-1];typeof J!="string"&&typeof te!="string"&&(F?te.focus():J.focus())}else Y.focus()};B.addEventListener("focusin",M),B.addEventListener("keydown",k,!0);const _=setInterval(()=>{B.activeElement&&B.activeElement.tagName==="BODY"&&M()},50);return()=>{clearInterval(_),B.removeEventListener("focusin",M),B.removeEventListener("keydown",k,!0)}},[o,i,u,d,p,f]);const N=B=>{x.current===null&&(x.current=B.relatedTarget),A.current=!0,R.current=B.target;const k=r.props.onFocus;k&&k(B)},U=B=>{x.current===null&&(x.current=B.relatedTarget),A.current=!0};return S.jsxs(C.Fragment,{children:[S.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:h,"data-testid":"sentinelStart"}),C.cloneElement(r,{ref:E,onFocus:N}),S.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:v,"data-testid":"sentinelEnd"})]})}function IE(n){return typeof n=="function"?n():n}function XE(n){return n?n.props.hasOwnProperty("in"):!1}const My=()=>{},fs=new LE;function KE(n){const{container:r,disableEscapeKeyDown:o=!1,disableScrollLock:i=!1,closeAfterTransition:u=!1,onTransitionEnter:f,onTransitionExited:d,children:p,onClose:g,open:h,rootRef:v}=n,x=C.useRef({}),R=C.useRef(null),A=C.useRef(null),T=Zt(A,v),[E,j]=C.useState(!h),N=XE(p);let U=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(U=!1);const B=()=>Bn(R.current),k=()=>(x.current.modalRef=A.current,x.current.mount=R.current,x.current),M=()=>{fs.mount(k(),{disableScrollLock:i}),A.current&&(A.current.scrollTop=0)},_=br(()=>{const G=IE(r)||B().body;fs.add(k(),G),A.current&&M()}),Y=()=>fs.isTopModal(k()),I=br(G=>{R.current=G,G&&(h&&Y()?M():A.current&&zo(A.current,U))}),F=C.useCallback(()=>{fs.remove(k(),U)},[U]);C.useEffect(()=>()=>{F()},[F]),C.useEffect(()=>{h?_():(!N||!u)&&F()},[h,F,N,u,_]);const J=G=>w=>{G.onKeyDown?.(w),!(w.key!=="Escape"||w.which===229||!Y())&&(o||(w.stopPropagation(),g&&g(w,"escapeKeyDown")))},te=G=>w=>{G.onClick?.(w),w.target===w.currentTarget&&g&&g(w,"backdropClick")};return{getRootProps:(G={})=>{const w=G0(n);delete w.onTransitionEnter,delete w.onTransitionExited;const K={...w,...G};return{role:"presentation",...K,onKeyDown:J(K),ref:T}},getBackdropProps:(G={})=>{const w=G;return{"aria-hidden":!0,...w,onClick:te(w),open:h}},getTransitionProps:()=>{const G=()=>{j(!1),f&&f()},w=()=>{j(!0),d&&d(),u&&F()};return{onEnter:uy(G,p?.props.onEnter??My),onExited:uy(w,p?.props.onExited??My)}},rootRef:T,portalRef:I,isTopModal:Y,exited:E,hasTransition:N}}function QE(n){return we("MuiModal",n)}De("MuiModal",["root","hidden","backdrop"]);const FE=n=>{const{open:r,exited:o,classes:i}=n;return ze({root:["root",!r&&o&&"hidden"],backdrop:["backdrop"]},QE,i)},ZE=fe("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.open&&o.exited&&r.hidden]}})(He(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),WE=fe(FT,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),JE=C.forwardRef(function(r,o){const i=Ne({name:"MuiModal",props:r}),{BackdropComponent:u=WE,BackdropProps:f,classes:d,className:p,closeAfterTransition:g=!1,children:h,container:v,component:x,components:R={},componentsProps:A={},disableAutoFocus:T=!1,disableEnforceFocus:E=!1,disableEscapeKeyDown:j=!1,disablePortal:N=!1,disableRestoreFocus:U=!1,disableScrollLock:B=!1,hideBackdrop:k=!1,keepMounted:M=!1,onClose:_,onTransitionEnter:Y,onTransitionExited:I,open:F,slotProps:J={},slots:te={},theme:b,...Q}=i,P={...i,closeAfterTransition:g,disableAutoFocus:T,disableEnforceFocus:E,disableEscapeKeyDown:j,disablePortal:N,disableRestoreFocus:U,disableScrollLock:B,hideBackdrop:k,keepMounted:M},{getRootProps:G,getBackdropProps:w,getTransitionProps:K,portalRef:oe,isTopModal:ne,exited:O,hasTransition:X}=KE({...P,rootRef:o}),le={...P,exited:O},re=FE(le),ue={};if(h.props.tabIndex===void 0&&(ue.tabIndex="-1"),X){const{onEnter:xe,onExited:Me}=K();ue.onEnter=xe,ue.onExited=Me}const ce={slots:{root:R.Root,backdrop:R.Backdrop,...te},slotProps:{...A,...J}},[se,Se]=Ye("root",{ref:o,elementType:ZE,externalForwardedProps:{...ce,...Q,component:x},getSlotProps:G,ownerState:le,className:ge(p,re?.root,!le.open&&le.exited&&re?.hidden)}),[Ce,_e]=Ye("backdrop",{ref:f?.ref,elementType:u,externalForwardedProps:ce,shouldForwardComponentProp:!0,additionalProps:f,getSlotProps:xe=>w({...xe,onClick:Me=>{xe?.onClick&&xe.onClick(Me)}}),className:ge(f?.className,re?.backdrop),ownerState:le});return!M&&!F&&(!X||O)?null:S.jsx(CT,{ref:oe,container:v,disablePortal:N,children:S.jsxs(se,{...Se,children:[!k&&u?S.jsx(Ce,{..._e}):null,S.jsx(YE,{disableEnforceFocus:E,disableAutoFocus:T,disableRestoreFocus:U,isEnabled:ne,open:F,children:C.cloneElement(h,ue)})]})})}),e5=n=>{const{classes:r,disableUnderline:o,startAdornment:i,endAdornment:u,size:f,hiddenLabel:d,multiline:p}=n,g={root:["root",!o&&"underline",i&&"adornedStart",u&&"adornedEnd",f==="small"&&`size${ie(f)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},h=ze(g,_T,r);return{...r,...h}},t5=fe(Xs,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...Ys(n,r),!o.disableUnderline&&r.underline]}})(He(({theme:n})=>{const r=n.palette.mode==="light",o=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",i=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",u=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",f=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:u,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i}},[`&.${pr.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i},[`&.${pr.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:f},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${pr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${pr.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?n.alpha(n.vars.palette.common.onBackground,n.vars.opacity.inputUnderline):o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${pr.disabled}, .${pr.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${pr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(jt()).map(([d])=>({props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[d]?.main}`}}})),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),n5=fe(Ks,{name:"MuiFilledInput",slot:"Input",overridesResolver:Is})(He(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),Ed=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:f={},componentsProps:d,fullWidth:p=!1,hiddenLabel:g,inputComponent:h="input",multiline:v=!1,slotProps:x,slots:R={},type:A="text",...T}=i,E={...i,disableUnderline:u,fullWidth:p,inputComponent:h,multiline:v,type:A},j=e5(i),N={root:{ownerState:E},input:{ownerState:E}},U=x??d?Ut(N,x??d):N,B=R.root??f.Root??t5,k=R.input??f.Input??n5;return S.jsx(Td,{slots:{root:B,input:k},slotProps:U,fullWidth:p,inputComponent:h,multiline:v,ref:o,type:A,...T,classes:j})});Ed.muiName="Input";function a5(n){return we("MuiFormControl",n)}De("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const r5=n=>{const{classes:r,margin:o,fullWidth:i}=n,u={root:["root",o!=="none"&&`margin${ie(o)}`,i&&"fullWidth"]};return ze(u,a5,r)},l5=fe("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`margin${ie(o.margin)}`],o.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),o5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiFormControl"}),{children:u,className:f,color:d="primary",component:p="div",disabled:g=!1,error:h=!1,focused:v,fullWidth:x=!1,hiddenLabel:R=!1,margin:A="none",required:T=!1,size:E="medium",variant:j="outlined",...N}=i,U={...i,color:d,component:p,disabled:g,error:h,fullWidth:x,hiddenLabel:R,margin:A,required:T,size:E,variant:j},B=r5(U),[k,M]=C.useState(()=>{let G=!1;return u&&C.Children.forEach(u,w=>{if(!Ef(w,["Input","Select"]))return;const K=Ef(w,["Select"])?w.props.input:w;K&&kT(K.props)&&(G=!0)}),G}),[_,Y]=C.useState(()=>{let G=!1;return u&&C.Children.forEach(u,w=>{Ef(w,["Input","Select"])&&(Ms(w.props,!0)||Ms(w.props.inputProps,!0))&&(G=!0)}),G}),[I,F]=C.useState(!1);g&&I&&F(!1);const J=v!==void 0&&!g?v:I;let te;C.useRef(!1);const b=C.useCallback(()=>{Y(!0)},[]),Q=C.useCallback(()=>{Y(!1)},[]),P=C.useMemo(()=>({adornedStart:k,setAdornedStart:M,color:d,disabled:g,error:h,filled:_,focused:J,fullWidth:x,hiddenLabel:R,size:E,onBlur:()=>{F(!1)},onFocus:()=>{F(!0)},onEmpty:Q,onFilled:b,registerEffect:te,required:T,variant:j}),[k,d,g,h,_,J,x,R,te,Q,b,T,E,j]);return S.jsx(Gs.Provider,{value:P,children:S.jsx(l5,{as:p,ownerState:U,className:ge(B.root,f),ref:o,...N,children:u})})});function i5(n){return we("MuiFormControlLabel",n)}const Oo=De("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),s5=n=>{const{classes:r,disabled:o,labelPlacement:i,error:u,required:f}=n,d={root:["root",o&&"disabled",`labelPlacement${ie(i)}`,u&&"error",f&&"required"],label:["label",o&&"disabled"],asterisk:["asterisk",u&&"error"]};return ze(d,i5,r)},u5=fe("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`& .${Oo.label}`]:r.label},r.root,r[`labelPlacement${ie(o.labelPlacement)}`]]}})(He(({theme:n})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Oo.disabled}`]:{cursor:"default"},[`& .${Oo.label}`]:{[`&.${Oo.disabled}`]:{color:(n.vars||n).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:r})=>r==="start"||r==="top"||r==="bottom",style:{marginLeft:16}}]}))),c5=fe("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(He(({theme:n})=>({[`&.${Oo.error}`]:{color:(n.vars||n).palette.error.main}}))),f5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiFormControlLabel"}),{checked:u,className:f,componentsProps:d={},control:p,disabled:g,disableTypography:h,inputRef:v,label:x,labelPlacement:R="end",name:A,onChange:T,required:E,slots:j={},slotProps:N={},value:U,...B}=i,k=ya(),M=g??p.props.disabled??k?.disabled,_=E??p.props.required,Y={disabled:M,required:_};["checked","name","onChange","value","inputRef"].forEach(G=>{typeof p.props[G]>"u"&&typeof i[G]<"u"&&(Y[G]=i[G])});const I=Cr({props:i,muiFormControl:k,states:["error"]}),F={...i,disabled:M,labelPlacement:R,required:_,error:I.error},J=s5(F),te={slots:j,slotProps:{...d,...N}},[b,Q]=Ye("typography",{elementType:st,externalForwardedProps:te,ownerState:F});let P=x;return P!=null&&P.type!==st&&!h&&(P=S.jsx(b,{component:"span",...Q,className:ge(J.label,Q?.className),children:P})),S.jsxs(u5,{className:ge(J.root,f),ownerState:F,ref:o,...B,children:[C.cloneElement(p,Y),_?S.jsxs("div",{children:[P,S.jsxs(c5,{ownerState:F,"aria-hidden":!0,className:J.asterisk,children:[" ","*"]})]}):P]})});function d5(n){return we("MuiFormHelperText",n)}const Oy=De("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var wy;const p5=n=>{const{classes:r,contained:o,size:i,disabled:u,error:f,filled:d,focused:p,required:g}=n,h={root:["root",u&&"disabled",f&&"error",i&&`size${ie(i)}`,o&&"contained",p&&"focused",d&&"filled",g&&"required"]};return ze(h,d5,r)},m5=fe("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.size&&r[`size${ie(o.size)}`],o.contained&&r.contained,o.filled&&r.filled]}})(He(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Oy.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Oy.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),h5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiFormHelperText"}),{children:u,className:f,component:d="p",disabled:p,error:g,filled:h,focused:v,margin:x,required:R,variant:A,...T}=i,E=ya(),j=Cr({props:i,muiFormControl:E,states:["variant","size","disabled","error","filled","focused","required"]}),N={...i,component:d,contained:j.variant==="filled"||j.variant==="outlined",variant:j.variant,size:j.size,disabled:j.disabled,error:j.error,filled:j.filled,focused:j.focused,required:j.required};delete N.ownerState;const U=p5(N);return S.jsx(m5,{as:d,className:ge(U.root,f),ref:o,...T,ownerState:N,children:u===" "?wy||(wy=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):u})});function g5(n){return we("MuiFormLabel",n)}const ko=De("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),y5=n=>{const{classes:r,color:o,focused:i,disabled:u,error:f,filled:d,required:p}=n,g={root:["root",`color${ie(o)}`,u&&"disabled",f&&"error",d&&"filled",i&&"focused",p&&"required"],asterisk:["asterisk",f&&"error"]};return ze(g,g5,r)},v5=fe("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color==="secondary"&&r.colorSecondary,o.filled&&r.filled]}})(He(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{color:r},style:{[`&.${ko.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${ko.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${ko.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),b5=fe("span",{name:"MuiFormLabel",slot:"Asterisk"})(He(({theme:n})=>({[`&.${ko.error}`]:{color:(n.vars||n).palette.error.main}}))),S5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiFormLabel"}),{children:u,className:f,color:d,component:p="label",disabled:g,error:h,filled:v,focused:x,required:R,...A}=i,T=ya(),E=Cr({props:i,muiFormControl:T,states:["color","required","focused","disabled","error","filled"]}),j={...i,color:E.color||"primary",component:p,disabled:E.disabled,error:E.error,filled:E.filled,focused:E.focused,required:E.required},N=y5(j);return S.jsxs(v5,{as:p,ownerState:j,className:ge(N.root,f),ref:o,...A,children:[u,E.required&&S.jsxs(b5,{ownerState:j,"aria-hidden":!0,className:N.asterisk,children:[" ","*"]})]})});function Ff(n){return`scale(${n}, ${n**2})`}const x5={entering:{opacity:1,transform:Ff(1)},entered:{opacity:1,transform:"none"}},Of=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Zf=C.forwardRef(function(r,o){const{addEndListener:i,appear:u=!0,children:f,easing:d,in:p,onEnter:g,onEntered:h,onEntering:v,onExit:x,onExited:R,onExiting:A,style:T,timeout:E="auto",TransitionComponent:j=Kn,...N}=r,U=H0(),B=C.useRef(),k=Vs(),M=C.useRef(null),_=Zt(M,Ko(f),o),Y=G=>w=>{if(G){const K=M.current;w===void 0?G(K):G(K,w)}},I=Y(v),F=Y((G,w)=>{q0(G);const{duration:K,delay:oe,easing:ne}=Ts({style:T,timeout:E,easing:d},{mode:"enter"});let O;E==="auto"?(O=k.transitions.getAutoHeightDuration(G.clientHeight),B.current=O):O=K,G.style.transition=[k.transitions.create("opacity",{duration:O,delay:oe}),k.transitions.create("transform",{duration:Of?O:O*.666,delay:oe,easing:ne})].join(","),g&&g(G,w)}),J=Y(h),te=Y(A),b=Y(G=>{const{duration:w,delay:K,easing:oe}=Ts({style:T,timeout:E,easing:d},{mode:"exit"});let ne;E==="auto"?(ne=k.transitions.getAutoHeightDuration(G.clientHeight),B.current=ne):ne=w,G.style.transition=[k.transitions.create("opacity",{duration:ne,delay:K}),k.transitions.create("transform",{duration:Of?ne:ne*.666,delay:Of?K:K||ne*.333,easing:oe})].join(","),G.style.opacity=0,G.style.transform=Ff(.75),x&&x(G)}),Q=Y(R),P=G=>{E==="auto"&&U.start(B.current||0,G),i&&i(M.current,G)};return S.jsx(j,{appear:u,in:p,nodeRef:M,onEnter:F,onEntered:J,onEntering:I,onExit:b,onExited:Q,onExiting:te,addEndListener:P,timeout:E==="auto"?null:E,...N,children:(G,{ownerState:w,...K})=>C.cloneElement(f,{style:{opacity:0,transform:Ff(.75),visibility:G==="exited"&&!p?"hidden":void 0,...x5[G],...T,...f.props.style},ref:_,...K})})});Zf&&(Zf.muiSupportAuto=!0);const C5=n=>{const{classes:r,disableUnderline:o}=n,u=ze({root:["root",!o&&"underline"],input:["input"]},DT,r);return{...r,...u}},T5=fe(Xs,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...Ys(n,r),!o.disableUnderline&&r.underline]}})(He(({theme:n})=>{let o=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(o=n.alpha(n.vars.palette.common.onBackground,n.vars.opacity.inputUnderline)),{position:"relative",variants:[{props:({ownerState:i})=>i.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:i})=>!i.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Co.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Co.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Co.disabled}, .${Co.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${o}`}},[`&.${Co.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(jt()).map(([i])=>({props:{color:i,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[i].main}`}}}))]}})),E5=fe(Ks,{name:"MuiInput",slot:"Input",overridesResolver:Is})({}),Rd=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiInput"}),{disableUnderline:u=!1,components:f={},componentsProps:d,fullWidth:p=!1,inputComponent:g="input",multiline:h=!1,slotProps:v,slots:x={},type:R="text",...A}=i,T=C5(i),j={root:{ownerState:{disableUnderline:u}}},N=v??d?Ut(v??d,j):j,U=x.root??f.Root??T5,B=x.input??f.Input??E5;return S.jsx(Td,{slots:{root:U,input:B},slotProps:N,fullWidth:p,inputComponent:g,multiline:h,ref:o,type:R,...A,classes:T})});Rd.muiName="Input";function R5(n){return we("MuiInputAdornment",n)}const zy=De("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var ky;const A5=(n,r)=>{const{ownerState:o}=n;return[r.root,r[`position${ie(o.position)}`],o.disablePointerEvents===!0&&r.disablePointerEvents,r[o.variant]]},M5=n=>{const{classes:r,disablePointerEvents:o,hiddenLabel:i,position:u,size:f,variant:d}=n,p={root:["root",o&&"disablePointerEvents",u&&`position${ie(u)}`,d,i&&"hiddenLabel",f&&`size${ie(f)}`]};return ze(p,R5,r)},O5=fe("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:A5})(He(({theme:n})=>({display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(n.vars||n).palette.action.active,variants:[{props:{variant:"filled"},style:{[`&.${zy.positionStart}&:not(.${zy.hiddenLabel})`]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}))),w5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiInputAdornment"}),{children:u,className:f,component:d="div",disablePointerEvents:p=!1,disableTypography:g=!1,position:h,variant:v,...x}=i,R=ya()||{};let A=v;v&&R.variant,R&&!A&&(A=R.variant);const T={...i,hiddenLabel:R.hiddenLabel,size:R.size,disablePointerEvents:p,position:h,variant:A},E=M5(T);return S.jsx(Gs.Provider,{value:null,children:S.jsx(O5,{as:d,ownerState:T,className:ge(E.root,f),ref:o,...x,children:typeof u=="string"&&!g?S.jsx(st,{color:"textSecondary",children:u}):S.jsxs(C.Fragment,{children:[h==="start"?ky||(ky=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):null,u]})})})});function z5(n){return we("MuiInputLabel",n)}De("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const k5=n=>{const{classes:r,formControl:o,size:i,shrink:u,disableAnimation:f,variant:d,required:p}=n,g={root:["root",o&&"formControl",!f&&"animated",u&&"shrink",i&&i!=="medium"&&`size${ie(i)}`,d],asterisk:[p&&"asterisk"]},h=ze(g,z5,r);return{...r,...h}},j5=fe(S5,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`& .${ko.asterisk}`]:r.asterisk},r.root,o.formControl&&r.formControl,o.size==="small"&&r.sizeSmall,o.shrink&&r.shrink,!o.disableAnimation&&r.animated,o.focused&&r.focused,r[o.variant]]}})(He(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="filled"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:o,size:i})=>r==="filled"&&o.shrink&&i==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="outlined"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),B5=C.forwardRef(function(r,o){const i=Ne({name:"MuiInputLabel",props:r}),{disableAnimation:u=!1,margin:f,shrink:d,variant:p,className:g,...h}=i,v=ya();let x=d;typeof x>"u"&&v&&(x=v.filled||v.focused||v.adornedStart);const R=Cr({props:i,muiFormControl:v,states:["size","variant","required","focused"]}),A={...i,disableAnimation:u,formControl:v,shrink:x,size:R.size,variant:R.variant,required:R.required,focused:R.focused},T=k5(A);return S.jsx(j5,{"data-shrink":x,ref:o,className:ge(T.root,g),...h,ownerState:A,classes:T})});function D5(n){return we("MuiLink",n)}const N5=De("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),_5=({theme:n,ownerState:r})=>{const o=r.color;if("colorSpace"in n&&n.colorSpace){const f=Vn(n,`palette.${o}.main`)||Vn(n,`palette.${o}`)||r.color;return n.alpha(f,.4)}const i=Vn(n,`palette.${o}.main`,!1)||Vn(n,`palette.${o}`,!1)||r.color,u=Vn(n,`palette.${o}.mainChannel`)||Vn(n,`palette.${o}Channel`);return"vars"in n&&u?`rgba(${u} / 0.4)`:$o(i,.4)},jy={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},$5=n=>{const{classes:r,component:o,focusVisible:i,underline:u}=n,f={root:["root",`underline${ie(u)}`,o==="button"&&"button",i&&"focusVisible"]};return ze(f,D5,r)},L5=fe(st,{name:"MuiLink",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`underline${ie(o.underline)}`],o.component==="button"&&r.button]}})(He(({theme:n})=>({variants:[{props:{underline:"none"},style:{textDecoration:"none"}},{props:{underline:"hover"},style:{textDecoration:"none","&:hover":{textDecoration:"underline"}}},{props:{underline:"always"},style:{textDecoration:"underline","&:hover":{textDecorationColor:"inherit"}}},{props:({underline:r,ownerState:o})=>r==="always"&&o.color!=="inherit",style:{textDecorationColor:"var(--Link-underlineColor)"}},{props:({underline:r,ownerState:o})=>r==="always"&&o.color==="inherit",style:n.colorSpace?{textDecorationColor:n.alpha("currentColor",.4)}:null},...Object.entries(n.palette).filter(jt()).map(([r])=>({props:{underline:"always",color:r},style:{"--Link-underlineColor":n.alpha((n.vars||n).palette[r].main,.4)}})),{props:{underline:"always",color:"textPrimary"},style:{"--Link-underlineColor":n.alpha((n.vars||n).palette.text.primary,.4)}},{props:{underline:"always",color:"textSecondary"},style:{"--Link-underlineColor":n.alpha((n.vars||n).palette.text.secondary,.4)}},{props:{underline:"always",color:"textDisabled"},style:{"--Link-underlineColor":(n.vars||n).palette.text.disabled}},{props:{component:"button"},style:{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},[`&.${N5.focusVisible}`]:{outline:"auto"}}}]}))),U5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiLink"}),u=Vs(),{className:f,color:d="primary",component:p="a",onBlur:g,onFocus:h,TypographyClasses:v,underline:x="always",variant:R="inherit",sx:A,...T}=i,[E,j]=C.useState(!1),N=M=>{Rs(M.target)||j(!1),g&&g(M)},U=M=>{Rs(M.target)&&j(!0),h&&h(M)},B={...i,color:d,component:p,focusVisible:E,underline:x,variant:R},k=$5(B);return S.jsx(L5,{color:d,className:ge(k.root,f),classes:v,component:p,onBlur:N,onFocus:U,ref:o,ownerState:B,variant:R,...T,sx:[...jy[d]===void 0?[{color:d}]:[],...Array.isArray(A)?A:[A]],style:{...T.style,...x==="always"&&d!=="inherit"&&!jy[d]&&{"--Link-underlineColor":_5({theme:u,ownerState:B})}}})}),H5=C.createContext({});function q5(n){return we("MuiList",n)}De("MuiList",["root","padding","dense","subheader"]);const P5=n=>{const{classes:r,disablePadding:o,dense:i,subheader:u}=n;return ze({root:["root",!o&&"padding",i&&"dense",u&&"subheader"]},q5,r)},V5=fe("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disablePadding&&r.padding,o.dense&&r.dense,o.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),G5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiList"}),{children:u,className:f,component:d="ul",dense:p=!1,disablePadding:g=!1,subheader:h,...v}=i,x=C.useMemo(()=>({dense:p}),[p]),R={...i,component:d,dense:p,disablePadding:g},A=P5(R);return S.jsx(H5.Provider,{value:x,children:S.jsxs(V5,{as:d,className:ge(A.root,f),ref:o,ownerState:R,...v,children:[h,u]})})});function wf(n,r,o){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:o?null:n.firstChild}function By(n,r,o){return n===r?o?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:o?null:n.lastChild}function Z0(n,r){if(r===void 0)return!0;let o=n.innerText;return o===void 0&&(o=n.textContent),o=o.trim().toLowerCase(),o.length===0?!1:r.repeating?o[0]===r.keys[0]:o.startsWith(r.keys.join(""))}function To(n,r,o,i,u,f){let d=!1,p=u(n,r,r?o:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const g=i?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!Z0(p,f)||g)p=u(n,p,o);else return p.focus(),!0}return!1}const Y5=C.forwardRef(function(r,o){const{actions:i,autoFocus:u=!1,autoFocusItem:f=!1,children:d,className:p,disabledItemsFocusable:g=!1,disableListWrap:h=!1,onKeyDown:v,variant:x="selectedMenu",...R}=r,A=C.useRef(null),T=C.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});ma(()=>{u&&A.current.focus()},[u]),C.useImperativeHandle(i,()=>({adjustStyleForScrollbar:(B,{direction:k})=>{const M=!A.current.style.width;if(B.clientHeight<A.current.clientHeight&&M){const _=`${F0(ha(B))}px`;A.current.style[k==="rtl"?"paddingLeft":"paddingRight"]=_,A.current.style.width=`calc(100% + ${_})`}return A.current}}),[]);const E=B=>{const k=A.current,M=B.key;if(B.ctrlKey||B.metaKey||B.altKey){v&&v(B);return}const Y=Bn(k).activeElement;if(M==="ArrowDown")B.preventDefault(),To(k,Y,h,g,wf);else if(M==="ArrowUp")B.preventDefault(),To(k,Y,h,g,By);else if(M==="Home")B.preventDefault(),To(k,null,h,g,wf);else if(M==="End")B.preventDefault(),To(k,null,h,g,By);else if(M.length===1){const I=T.current,F=M.toLowerCase(),J=performance.now();I.keys.length>0&&(J-I.lastTime>500?(I.keys=[],I.repeating=!0,I.previousKeyMatched=!0):I.repeating&&F!==I.keys[0]&&(I.repeating=!1)),I.lastTime=J,I.keys.push(F);const te=Y&&!I.repeating&&Z0(Y,I);I.previousKeyMatched&&(te||To(k,Y,!1,g,wf,I))?B.preventDefault():I.previousKeyMatched=!1}v&&v(B)},j=Zt(A,o);let N=-1;C.Children.forEach(d,(B,k)=>{if(!C.isValidElement(B)){N===k&&(N+=1,N>=d.length&&(N=-1));return}B.props.disabled||(x==="selectedMenu"&&B.props.selected||N===-1)&&(N=k),N===k&&(B.props.disabled||B.props.muiSkipListHighlight||B.type.muiSkipListHighlight)&&(N+=1,N>=d.length&&(N=-1))});const U=C.Children.map(d,(B,k)=>{if(k===N){const M={};return f&&(M.autoFocus=!0),B.props.tabIndex===void 0&&x==="selectedMenu"&&(M.tabIndex=0),C.cloneElement(B,M)}return B});return S.jsx(G5,{role:"menu",ref:j,className:p,onKeyDown:E,tabIndex:u?0:-1,...R,children:U})});function I5(n){return we("MuiPopover",n)}De("MuiPopover",["root","paper"]);function Dy(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.height/2:r==="bottom"&&(o=n.height),o}function Ny(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.width/2:r==="right"&&(o=n.width),o}function _y(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function ds(n){return typeof n=="function"?n():n}const X5=n=>{const{classes:r}=n;return ze({root:["root"],paper:["paper"]},I5,r)},K5=fe(JE,{name:"MuiPopover",slot:"Root"})({}),W0=fe(ga,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),Q5=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiPopover"}),{action:u,anchorEl:f,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:g="anchorEl",children:h,className:v,container:x,elevation:R=8,marginThreshold:A=16,open:T,PaperProps:E={},slots:j={},slotProps:N={},transformOrigin:U={vertical:"top",horizontal:"left"},TransitionComponent:B,transitionDuration:k="auto",TransitionProps:M={},disableScrollLock:_=!1,...Y}=i,I=C.useRef(),F={...i,anchorOrigin:d,anchorReference:g,elevation:R,marginThreshold:A,transformOrigin:U,TransitionComponent:B,transitionDuration:k,TransitionProps:M},J=X5(F),te=C.useCallback(()=>{if(g==="anchorPosition")return p;const xe=ds(f),$e=(xe&&xe.nodeType===1?xe:Bn(I.current).body).getBoundingClientRect();return{top:$e.top+Dy($e,d.vertical),left:$e.left+Ny($e,d.horizontal)}},[f,d.horizontal,d.vertical,p,g]),b=C.useCallback(xe=>({vertical:Dy(xe,U.vertical),horizontal:Ny(xe,U.horizontal)}),[U.horizontal,U.vertical]),Q=C.useCallback(xe=>{const Me={width:xe.offsetWidth,height:xe.offsetHeight},$e=b(Me);if(g==="none")return{top:null,left:null,transformOrigin:_y($e)};const St=te();let Te=St.top-$e.vertical,Ke=St.left-$e.horizontal;const Ht=Te+Me.height,Qe=Ke+Me.width,pt=ha(ds(f)),dt=pt.innerHeight-A,yt=pt.innerWidth-A;if(A!==null&&Te<A){const tt=Te-A;Te-=tt,$e.vertical+=tt}else if(A!==null&&Ht>dt){const tt=Ht-dt;Te-=tt,$e.vertical+=tt}if(A!==null&&Ke<A){const tt=Ke-A;Ke-=tt,$e.horizontal+=tt}else if(Qe>yt){const tt=Qe-yt;Ke-=tt,$e.horizontal+=tt}return{top:`${Math.round(Te)}px`,left:`${Math.round(Ke)}px`,transformOrigin:_y($e)}},[f,g,te,b,A]),[P,G]=C.useState(T),w=C.useCallback(()=>{const xe=I.current;if(!xe)return;const Me=Q(xe);Me.top!==null&&xe.style.setProperty("top",Me.top),Me.left!==null&&(xe.style.left=Me.left),xe.style.transformOrigin=Me.transformOrigin,G(!0)},[Q]);C.useEffect(()=>(_&&window.addEventListener("scroll",w),()=>window.removeEventListener("scroll",w)),[f,_,w]);const K=()=>{w()},oe=()=>{G(!1)};C.useEffect(()=>{T&&w()}),C.useImperativeHandle(u,()=>T?{updatePosition:()=>{w()}}:null,[T,w]),C.useEffect(()=>{if(!T)return;const xe=D0(()=>{w()}),Me=ha(ds(f));return Me.addEventListener("resize",xe),()=>{xe.clear(),Me.removeEventListener("resize",xe)}},[f,T,w]);let ne=k;const O={slots:{transition:B,...j},slotProps:{transition:M,paper:E,...N}},[X,le]=Ye("transition",{elementType:Zf,externalForwardedProps:O,ownerState:F,getSlotProps:xe=>({...xe,onEntering:(Me,$e)=>{xe.onEntering?.(Me,$e),K()},onExited:Me=>{xe.onExited?.(Me),oe()}}),additionalProps:{appear:!0,in:T}});k==="auto"&&!X.muiSupportAuto&&(ne=void 0);const re=x||(f?Bn(ds(f)).body:void 0),[ue,{slots:ce,slotProps:se,...Se}]=Ye("root",{ref:o,elementType:K5,externalForwardedProps:{...O,...Y},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:j.backdrop},slotProps:{backdrop:N0(typeof N.backdrop=="function"?N.backdrop(F):N.backdrop,{invisible:!0})},container:re,open:T},ownerState:F,className:ge(J.root,v)}),[Ce,_e]=Ye("paper",{ref:I,className:J.paper,elementType:W0,externalForwardedProps:O,shouldForwardComponentProp:!0,additionalProps:{elevation:R,style:P?void 0:{opacity:0}},ownerState:F});return S.jsx(ue,{...Se,...!Es(ue)&&{slots:ce,slotProps:se,disableScrollLock:_},children:S.jsx(X,{...le,timeout:ne,children:S.jsx(Ce,{..._e,children:h})})})});function F5(n){return we("MuiMenu",n)}De("MuiMenu",["root","paper","list"]);const Z5={vertical:"top",horizontal:"right"},W5={vertical:"top",horizontal:"left"},J5=n=>{const{classes:r}=n;return ze({root:["root"],paper:["paper"],list:["list"]},F5,r)},e3=fe(Q5,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),t3=fe(W0,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),n3=fe(Y5,{name:"MuiMenu",slot:"List"})({outline:0}),a3=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiMenu"}),{autoFocus:u=!0,children:f,className:d,disableAutoFocusItem:p=!1,MenuListProps:g={},onClose:h,open:v,PaperProps:x={},PopoverClasses:R,transitionDuration:A="auto",TransitionProps:{onEntering:T,...E}={},variant:j="selectedMenu",slots:N={},slotProps:U={},...B}=i,k=X2(),M={...i,autoFocus:u,disableAutoFocusItem:p,MenuListProps:g,onEntering:T,PaperProps:x,transitionDuration:A,TransitionProps:E,variant:j},_=J5(M),Y=u&&!p&&v,I=C.useRef(null),F=(ne,O)=>{I.current&&I.current.adjustStyleForScrollbar(ne,{direction:k?"rtl":"ltr"}),T&&T(ne,O)},J=ne=>{ne.key==="Tab"&&(ne.preventDefault(),h&&h(ne,"tabKeyDown"))};let te=-1;C.Children.map(f,(ne,O)=>{C.isValidElement(ne)&&(ne.props.disabled||(j==="selectedMenu"&&ne.props.selected||te===-1)&&(te=O))});const b={slots:N,slotProps:{list:g,transition:E,paper:x,...U}},Q=ST({elementType:N.root,externalSlotProps:U.root,ownerState:M,className:[_.root,d]}),[P,G]=Ye("paper",{className:_.paper,elementType:t3,externalForwardedProps:b,shouldForwardComponentProp:!0,ownerState:M}),[w,K]=Ye("list",{className:ge(_.list,g.className),elementType:n3,shouldForwardComponentProp:!0,externalForwardedProps:b,getSlotProps:ne=>({...ne,onKeyDown:O=>{J(O),ne.onKeyDown?.(O)}}),ownerState:M}),oe=typeof b.slotProps.transition=="function"?b.slotProps.transition(M):b.slotProps.transition;return S.jsx(e3,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:k?"right":"left"},transformOrigin:k?Z5:W5,slots:{root:N.root,paper:P,backdrop:N.backdrop,...N.transition&&{transition:N.transition}},slotProps:{root:Q,paper:G,backdrop:typeof U.backdrop=="function"?U.backdrop(M):U.backdrop,transition:{...oe,onEntering:(...ne)=>{F(...ne),oe?.onEntering?.(...ne)}}},open:v,ref:o,transitionDuration:A,ownerState:M,...B,classes:R,children:S.jsx(w,{actions:I,autoFocus:u&&(te===-1||p),autoFocusItem:Y,variant:j,...K,children:f})})});function r3(n){return we("MuiNativeSelect",n)}const Ad=De("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),l3=n=>{const{classes:r,variant:o,disabled:i,multiple:u,open:f,error:d}=n,p={select:["select",o,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${ie(o)}`,f&&"iconOpen",i&&"disabled"]};return ze(p,r3,r)},J0=fe("select",{name:"MuiNativeSelect"})(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Ad.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),o3=fe(J0,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:hn,overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.select,r[o.variant],o.error&&r.error,{[`&.${Ad.multiple}`]:r.multiple}]}})({}),ev=fe("svg",{name:"MuiNativeSelect"})(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${Ad.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),i3=fe(ev,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${ie(o.variant)}`],o.open&&r.iconOpen]}})({}),s3=C.forwardRef(function(r,o){const{className:i,disabled:u,error:f,IconComponent:d,inputRef:p,variant:g="standard",...h}=r,v={...r,disabled:u,variant:g,error:f},x=l3(v);return S.jsxs(C.Fragment,{children:[S.jsx(o3,{ownerState:v,className:ge(x.select,i),disabled:u,ref:p||o,...h}),r.multiple?null:S.jsx(i3,{as:d,ownerState:v,className:x.icon})]})});var $y;const u3=fe("fieldset",{name:"MuiNotchedOutlined",shouldForwardProp:hn})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),c3=fe("legend",{name:"MuiNotchedOutlined",shouldForwardProp:hn})(He(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function f3(n){const{children:r,classes:o,className:i,label:u,notched:f,...d}=n,p=u!=null&&u!=="",g={...n,notched:f,withLabel:p};return S.jsx(u3,{"aria-hidden":!0,className:i,ownerState:g,...d,children:S.jsx(c3,{ownerState:g,children:p?S.jsx("span",{children:u}):$y||($y=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const d3=n=>{const{classes:r}=n,i=ze({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},NT,r);return{...r,...i}},p3=fe(Xs,{shouldForwardProp:n=>hn(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Ys})(He(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${Hn.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${Hn.notchedOutline}`]:{borderColor:n.vars?n.alpha(n.vars.palette.common.onBackground,.23):r}},[`&.${Hn.focused} .${Hn.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(jt()).map(([o])=>({props:{color:o},style:{[`&.${Hn.focused} .${Hn.notchedOutline}`]:{borderColor:(n.vars||n).palette[o].main}}})),{props:{},style:{[`&.${Hn.error} .${Hn.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${Hn.disabled} .${Hn.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:o})=>o.startAdornment,style:{paddingLeft:14}},{props:({ownerState:o})=>o.endAdornment,style:{paddingRight:14}},{props:({ownerState:o})=>o.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:o,size:i})=>o.multiline&&i==="small",style:{padding:"8.5px 14px"}}]}})),m3=fe(f3,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(He(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?n.alpha(n.vars.palette.common.onBackground,.23):r}})),h3=fe(Ks,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:Is})(He(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),Md=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiOutlinedInput"}),{components:u={},fullWidth:f=!1,inputComponent:d="input",label:p,multiline:g=!1,notched:h,slots:v={},slotProps:x={},type:R="text",...A}=i,T=d3(i),E=ya(),j=Cr({props:i,muiFormControl:E,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),N={...i,color:j.color||"primary",disabled:j.disabled,error:j.error,focused:j.focused,formControl:E,fullWidth:f,hiddenLabel:j.hiddenLabel,multiline:g,size:j.size,type:R},U=v.root??u.Root??p3,B=v.input??u.Input??h3,[k,M]=Ye("notchedOutline",{elementType:m3,className:T.notchedOutline,shouldForwardComponentProp:!0,ownerState:N,externalForwardedProps:{slots:v,slotProps:x},additionalProps:{label:p!=null&&p!==""&&j.required?S.jsxs(C.Fragment,{children:[p," ","*"]}):p}});return S.jsx(Td,{slots:{root:U,input:B},slotProps:x,renderSuffix:_=>S.jsx(k,{...M,notched:typeof h<"u"?h:!!(_.startAdornment||_.filled||_.focused)}),fullWidth:f,inputComponent:d,multiline:g,ref:o,type:R,...A,classes:{...T,notchedOutline:null}})});Md.muiName="Input";function tv(n){return we("MuiSelect",n)}const Eo=De("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var Ly;const g3=fe(J0,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`&.${Eo.select}`]:r.select},{[`&.${Eo.select}`]:r[o.variant]},{[`&.${Eo.error}`]:r.error},{[`&.${Eo.multiple}`]:r.multiple}]}})({[`&.${Eo.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),y3=fe(ev,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${ie(o.variant)}`],o.open&&r.iconOpen]}})({}),v3=fe("input",{shouldForwardProp:n=>B0(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function Uy(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function b3(n){return n==null||typeof n=="string"&&!n.trim()}const S3=n=>{const{classes:r,variant:o,disabled:i,multiple:u,open:f,error:d}=n,p={select:["select",o,i&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${ie(o)}`,f&&"iconOpen",i&&"disabled"],nativeInput:["nativeInput"]};return ze(p,tv,r)},x3=C.forwardRef(function(r,o){const{"aria-describedby":i,"aria-label":u,autoFocus:f,autoWidth:d,children:p,className:g,defaultOpen:h,defaultValue:v,disabled:x,displayEmpty:R,error:A=!1,IconComponent:T,inputRef:E,labelId:j,MenuProps:N={},multiple:U,name:B,onBlur:k,onChange:M,onClose:_,onFocus:Y,onOpen:I,open:F,readOnly:J,renderValue:te,required:b,SelectDisplayProps:Q={},tabIndex:P,type:G,value:w,variant:K="standard",...oe}=r,[ne,O]=qf({controlled:w,default:v,name:"Select"}),[X,le]=qf({controlled:F,default:h,name:"Select"}),re=C.useRef(null),ue=C.useRef(null),[ce,se]=C.useState(null),{current:Se}=C.useRef(F!=null),[Ce,_e]=C.useState(),xe=Zt(o,E),Me=C.useCallback(he=>{ue.current=he,he&&se(he)},[]),$e=ce?.parentNode;C.useImperativeHandle(xe,()=>({focus:()=>{ue.current.focus()},node:re.current,value:ne}),[ne]),C.useEffect(()=>{h&&X&&ce&&!Se&&(_e(d?null:$e.clientWidth),ue.current.focus())},[ce,d]),C.useEffect(()=>{f&&ue.current.focus()},[f]),C.useEffect(()=>{if(!j)return;const he=Bn(ue.current).getElementById(j);if(he){const Ie=()=>{getSelection().isCollapsed&&ue.current.focus()};return he.addEventListener("click",Ie),()=>{he.removeEventListener("click",Ie)}}},[j]);const St=(he,Ie)=>{he?I&&I(Ie):_&&_(Ie),Se||(_e(d?null:$e.clientWidth),le(he))},Te=he=>{he.button===0&&(he.preventDefault(),ue.current.focus(),St(!0,he))},Ke=he=>{St(!1,he)},Ht=C.Children.toArray(p),Qe=he=>{const Ie=Ht.find(vt=>vt.props.value===he.target.value);Ie!==void 0&&(O(Ie.props.value),M&&M(he,Ie))},pt=he=>Ie=>{let vt;if(Ie.currentTarget.hasAttribute("tabindex")){if(U){vt=Array.isArray(ne)?ne.slice():[];const ba=ne.indexOf(he.props.value);ba===-1?vt.push(he.props.value):vt.splice(ba,1)}else vt=he.props.value;if(he.props.onClick&&he.props.onClick(Ie),ne!==vt&&(O(vt),M)){const ba=Ie.nativeEvent||Ie,xl=new ba.constructor(ba.type,ba);Object.defineProperty(xl,"target",{writable:!0,value:{value:vt,name:B}}),M(xl,he)}U||St(!1,Ie)}},dt=he=>{J||[" ","ArrowUp","ArrowDown","Enter"].includes(he.key)&&(he.preventDefault(),St(!0,he))},yt=ce!==null&&X,tt=he=>{!yt&&k&&(Object.defineProperty(he,"target",{writable:!0,value:{value:ne,name:B}}),k(he))};delete oe["aria-invalid"];let pe,rn;const xt=[];let On=!1;(Ms({value:ne})||R)&&(te?pe=te(ne):On=!0);const mt=Ht.map(he=>{if(!C.isValidElement(he))return null;let Ie;if(U){if(!Array.isArray(ne))throw new Error(pa(2));Ie=ne.some(vt=>Uy(vt,he.props.value)),Ie&&On&&xt.push(he.props.children)}else Ie=Uy(ne,he.props.value),Ie&&On&&(rn=he.props.children);return C.cloneElement(he,{"aria-selected":Ie?"true":"false",onClick:pt(he),onKeyUp:vt=>{vt.key===" "&&vt.preventDefault(),he.props.onKeyUp&&he.props.onKeyUp(vt)},role:"option",selected:Ie,value:void 0,"data-value":he.props.value})});On&&(U?xt.length===0?pe=null:pe=xt.reduce((he,Ie,vt)=>(he.push(Ie),vt<xt.length-1&&he.push(", "),he),[]):pe=rn);let Ee=Ce;!d&&Se&&ce&&(Ee=$e.clientWidth);let nt;typeof P<"u"?nt=P:nt=x?null:0;const qe=Q.id||(B?`mui-component-select-${B}`:void 0),Qt={...r,variant:K,value:ne,open:yt,error:A},at=S3(Qt),va={...N.PaperProps,...typeof N.slotProps?.paper=="function"?N.slotProps.paper(Qt):N.slotProps?.paper},Qn={...N.MenuListProps,...typeof N.slotProps?.list=="function"?N.slotProps.list(Qt):N.slotProps?.list},Ka=Xo();return S.jsxs(C.Fragment,{children:[S.jsx(g3,{as:"div",ref:Me,tabIndex:nt,role:"combobox","aria-controls":yt?Ka:void 0,"aria-disabled":x?"true":void 0,"aria-expanded":yt?"true":"false","aria-haspopup":"listbox","aria-label":u,"aria-labelledby":[j,qe].filter(Boolean).join(" ")||void 0,"aria-describedby":i,"aria-required":b?"true":void 0,"aria-invalid":A?"true":void 0,onKeyDown:dt,onMouseDown:x||J?null:Te,onBlur:tt,onFocus:Y,...Q,ownerState:Qt,className:ge(Q.className,at.select,g),id:qe,children:b3(pe)?Ly||(Ly=S.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):pe}),S.jsx(v3,{"aria-invalid":A,value:Array.isArray(ne)?ne.join(","):ne,name:B,ref:re,"aria-hidden":!0,onChange:Qe,tabIndex:-1,disabled:x,className:at.nativeInput,autoFocus:f,required:b,...oe,ownerState:Qt}),S.jsx(y3,{as:T,className:at.icon,ownerState:Qt}),S.jsx(a3,{id:`menu-${B||""}`,anchorEl:$e,open:yt,onClose:Ke,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...N,slotProps:{...N.slotProps,list:{"aria-labelledby":j,role:"listbox","aria-multiselectable":U?"true":void 0,disableListWrap:!0,id:Ka,...Qn},paper:{...va,style:{minWidth:Ee,...va!=null?va.style:null}}},children:mt})]})}),C3=n=>{const{classes:r}=n,i=ze({root:["root"]},tv,r);return{...r,...i}},Od={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>hn(n)&&n!=="variant"},T3=fe(Rd,Od)(""),E3=fe(Md,Od)(""),R3=fe(Ed,Od)(""),nv=C.forwardRef(function(r,o){const i=Ne({name:"MuiSelect",props:r}),{autoWidth:u=!1,children:f,classes:d={},className:p,defaultOpen:g=!1,displayEmpty:h=!1,IconComponent:v=$T,id:x,input:R,inputProps:A,label:T,labelId:E,MenuProps:j,multiple:N=!1,native:U=!1,onClose:B,onOpen:k,open:M,renderValue:_,SelectDisplayProps:Y,variant:I="outlined",...F}=i,J=U?s3:x3,te=ya(),b=Cr({props:i,muiFormControl:te,states:["variant","error"]}),Q=b.variant||I,P={...i,variant:Q,classes:d},G=C3(P),{root:w,...K}=G,oe=R||{standard:S.jsx(T3,{ownerState:P}),outlined:S.jsx(E3,{label:T,ownerState:P}),filled:S.jsx(R3,{ownerState:P})}[Q],ne=Zt(o,Ko(oe));return S.jsx(C.Fragment,{children:C.cloneElement(oe,{inputComponent:J,inputProps:{children:f,error:b.error,IconComponent:v,variant:Q,type:void 0,multiple:N,...U?{id:x}:{autoWidth:u,defaultOpen:g,displayEmpty:h,labelId:E,MenuProps:j,onClose:B,onOpen:k,open:M,renderValue:_,SelectDisplayProps:{id:x,...Y}},...A,classes:A?Ut(K,A.classes):K,...R?R.props.inputProps:{}},...(N&&U||h)&&Q==="outlined"?{notched:!0}:{},ref:ne,className:ge(oe.props.className,p,G.root),...!R&&{variant:Q},...F})})});nv.muiName="Select";const A3=Rx({createStyledComponent:fe("div",{name:"MuiStack",slot:"Root"}),useThemeProps:n=>Ne({props:n,name:"MuiStack"})});function M3(n){return we("MuiToolbar",n)}De("MuiToolbar",["root","gutters","regular","dense"]);const O3=n=>{const{classes:r,disableGutters:o,variant:i}=n;return ze({root:["root",!o&&"gutters",i]},M3,r)},w3=fe("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disableGutters&&r.gutters,r[o.variant]]}})(He(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:n.spacing(2),paddingRight:n.spacing(2),[n.breakpoints.up("sm")]:{paddingLeft:n.spacing(3),paddingRight:n.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:n.mixins.toolbar}]}))),z3=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiToolbar"}),{className:u,component:f="div",disableGutters:d=!1,variant:p="regular",...g}=i,h={...i,component:f,disableGutters:d,variant:p},v=O3(h);return S.jsx(w3,{as:f,className:ge(v.root,u),ref:o,ownerState:h,...g})});function k3(n){return we("MuiTextField",n)}De("MuiTextField",["root"]);const j3={standard:Rd,filled:Ed,outlined:Md},B3=n=>{const{classes:r}=n;return ze({root:["root"]},k3,r)},D3=fe(o5,{name:"MuiTextField",slot:"Root"})({}),N3=C.forwardRef(function(r,o){const i=Ne({props:r,name:"MuiTextField"}),{autoComplete:u,autoFocus:f=!1,children:d,className:p,color:g="primary",defaultValue:h,disabled:v=!1,error:x=!1,FormHelperTextProps:R,fullWidth:A=!1,helperText:T,id:E,InputLabelProps:j,inputProps:N,InputProps:U,inputRef:B,label:k,maxRows:M,minRows:_,multiline:Y=!1,name:I,onBlur:F,onChange:J,onFocus:te,placeholder:b,required:Q=!1,rows:P,select:G=!1,SelectProps:w,slots:K={},slotProps:oe={},type:ne,value:O,variant:X="outlined",...le}=i,re={...i,autoFocus:f,color:g,disabled:v,error:x,fullWidth:A,multiline:Y,required:Q,select:G,variant:X},ue=B3(re),ce=Xo(E),se=T&&ce?`${ce}-helper-text`:void 0,Se=k&&ce?`${ce}-label`:void 0,Ce=j3[X],_e={slots:K,slotProps:{input:U,inputLabel:j,htmlInput:N,formHelperText:R,select:w,...oe}},xe={},Me=_e.slotProps.inputLabel;X==="outlined"&&(Me&&typeof Me.shrink<"u"&&(xe.notched=Me.shrink),xe.label=k),G&&((!w||!w.native)&&(xe.id=void 0),xe["aria-describedby"]=void 0);const[$e,St]=Ye("root",{elementType:D3,shouldForwardComponentProp:!0,externalForwardedProps:{..._e,...le},ownerState:re,className:ge(ue.root,p),ref:o,additionalProps:{disabled:v,error:x,fullWidth:A,required:Q,color:g,variant:X}}),[Te,Ke]=Ye("input",{elementType:Ce,externalForwardedProps:_e,additionalProps:xe,ownerState:re}),[Ht,Qe]=Ye("inputLabel",{elementType:B5,externalForwardedProps:_e,ownerState:re}),[pt,dt]=Ye("htmlInput",{elementType:"input",externalForwardedProps:_e,ownerState:re}),[yt,tt]=Ye("formHelperText",{elementType:h5,externalForwardedProps:_e,ownerState:re}),[pe,rn]=Ye("select",{elementType:nv,externalForwardedProps:_e,ownerState:re}),xt=S.jsx(Te,{"aria-describedby":se,autoComplete:u,autoFocus:f,defaultValue:h,fullWidth:A,multiline:Y,name:I,rows:P,maxRows:M,minRows:_,type:ne,value:O,id:ce,inputRef:B,onBlur:F,onChange:J,onFocus:te,placeholder:b,inputProps:dt,slots:{input:K.htmlInput?pt:void 0},...Ke});return S.jsxs($e,{...St,children:[k!=null&&k!==""&&S.jsx(Ht,{htmlFor:ce,id:Se,...Qe,children:k}),G?S.jsx(pe,{"aria-describedby":se,id:ce,labelId:Se,value:O,input:xt,...rn,children:d}):xt,T&&S.jsx(yt,{id:se,...tt,children:T})]})}),wd=ft(S.jsx("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"})),Hy=ft(S.jsx("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"})),qy=ft(S.jsx("path",{d:"M4 9h4v11H4zm12 4h4v7h-4zm-6-9h4v16h-4z"})),Py=ft(S.jsx("path",{d:"M12 7V3H2v18h20V7zM6 19H4v-2h2zm0-4H4v-2h2zm0-4H4V9h2zm0-4H4V5h2zm4 12H8v-2h2zm0-4H8v-2h2zm0-4H8V9h2zm0-4H8V5h2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8zm-2-8h-2v2h2zm0 4h-2v2h2z"})),_3=ft(S.jsx("path",{d:"M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 18H4V8h16z"})),av=ft(S.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"})),rv=ft(S.jsx("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"})),$3=ft(S.jsx("path",{d:"M10.09 15.59 11.5 17l5-5-5-5-1.41 1.41L12.67 11H3v2h9.67zM19 3H5c-1.11 0-2 .9-2 2v4h2V5h14v14H5v-4H3v4c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"})),L3=ft(S.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),Vy=ft(S.jsx("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"})),U3=ft(S.jsx("path",{d:"M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"})),H3=ft(S.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"})),q3=ft(S.jsx("path",{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5z"})),P3=ft(S.jsx("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"})),V3=ft(S.jsx("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"})),yl=({variant:n="outlined",...r})=>S.jsx(N3,{variant:n,fullWidth:!0,margin:"normal",...r,sx:{"& .MuiOutlinedInput-root":{borderRadius:2,"& fieldset":{borderColor:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderColor:"primary.main",borderWidth:2}},...r.sx}}),Sr=({children:n,loading:r=!1,loadingText:o,disabled:i,...u})=>S.jsx(iE,{...u,disabled:i||r,sx:{borderRadius:2,textTransform:"none",fontWeight:500,py:1.5,...u.sx},children:r?S.jsxs(S.Fragment,{children:[S.jsx(Cd,{size:20,color:"inherit",sx:{mr:1}}),o||n]}):n}),Qs=({children:n,title:r,subtitle:o,maxWidth:i=480})=>S.jsx(Xf,{elevation:8,sx:{borderRadius:3,maxWidth:i,width:"100%",mx:"auto"},children:S.jsxs(Kf,{sx:{p:4},children:[r&&S.jsxs(et,{sx:{textAlign:"center",mb:4},children:[S.jsx(st,{variant:"h5",component:"h2",sx:{mb:1},children:r}),o&&S.jsx(st,{variant:"body2",color:"text.secondary",children:o})]}),n]})}),jo=({message:n,onClose:r,...o})=>S.jsx(cT,{...o,onClose:r,sx:{borderRadius:2,mb:2,...o.sx},children:n}),Fs=()=>S.jsxs(et,{sx:{textAlign:"center",mb:4},children:[S.jsx(Ya,{sx:{width:64,height:64,bgcolor:"primary.main",mx:"auto",mb:2,background:"linear-gradient(135deg, #29b6f6 0%, #0277bd 100%)"},children:S.jsx(st,{variant:"h4",sx:{color:"white"},children:"OMS"})}),S.jsx(st,{variant:"h4",component:"h1",sx:{color:"text.primary",mb:1},children:"XOGOHRM"}),S.jsx(st,{variant:"body1",color:"text.secondary",children:"PMS + HRMS Solution"})]}),ps=n=>new Promise(r=>setTimeout(r,n)),zd={async login(n){if(await ps(1500),!n.email||!n.password)throw new Error("Email and password are required");if(n.email==="<EMAIL>"&&n.password==="admin123")return{success:!0,message:"Login successful",data:{user:{id:"1",email:n.email,name:"Admin User",role:"admin"},token:"mock-jwt-token"}};throw new Error("Invalid email or password")},async requestPasswordReset(n){if(await ps(2e3),!n.email)throw new Error("Email is required");return{success:!0,message:"Password reset link sent successfully"}},async requestAdminReset(n){if(await ps(2e3),!n.email||!n.employeeId||!n.reason)throw new Error("All fields are required");return{success:!0,message:"Admin reset request submitted successfully",data:{requestId:`PWR-${Date.now().toString().slice(-6)}`}}},async logout(){await ps(500),localStorage.removeItem("auth-token")}},G3=({onForgotPassword:n,onLoginSuccess:r})=>{const[o,i]=C.useState({email:"",password:"",rememberMe:!1}),[u,f]=C.useState(!1),[d,p]=C.useState(!1),[g,h]=C.useState(null),[v,x]=C.useState(null),R=T=>E=>{const j=T==="rememberMe"?E.target.checked:E.target.value;i(N=>({...N,[T]:j})),h(null)},A=async T=>{T.preventDefault(),p(!0),h(null),x(null);try{const E=await zd.login(o);x("Login successful! Redirecting..."),localStorage.setItem("auth-token",E.data?.token),setTimeout(()=>{r()},1e3)}catch(E){h(E instanceof Error?E.message:"Login failed")}finally{p(!1)}};return S.jsx(et,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)",display:"flex",alignItems:"center",justifyContent:"center",p:2},children:S.jsxs(Qo,{maxWidth:"sm",children:[S.jsx(Fs,{}),S.jsxs(Qs,{title:"Welcome Back",subtitle:"Sign in to your account to continue",children:[g&&S.jsx(jo,{severity:"error",message:g,onClose:()=>h(null)}),v&&S.jsx(jo,{severity:"success",message:v}),S.jsxs(et,{component:"form",onSubmit:A,sx:{mt:2},children:[S.jsx(yl,{label:"Email Address",type:"email",value:o.email,onChange:R("email"),required:!0,autoComplete:"email",placeholder:"Enter your email address"}),S.jsx(yl,{label:"Password",type:u?"text":"password",value:o.password,onChange:R("password"),required:!0,autoComplete:"current-password",placeholder:"Enter your password",InputProps:{endAdornment:S.jsx(w5,{position:"end",children:S.jsx(I0,{onClick:()=>f(!u),edge:"end","aria-label":"toggle password visibility",children:u?S.jsx(V3,{}):S.jsx(P3,{})})})}}),S.jsx(f5,{control:S.jsx(OE,{checked:o.rememberMe,onChange:R("rememberMe"),color:"primary"}),label:"Remember me",sx:{mb:3,mt:1}}),S.jsx(Sr,{type:"submit",variant:"contained",fullWidth:!0,size:"large",loading:d,loadingText:"Signing in...",sx:{mb:3,background:"linear-gradient(135deg, #29b6f6 0%, #0277bd 100%)","&:hover":{background:"linear-gradient(135deg, #0277bd 0%, #01579b 100%)"}},children:"Sign In"}),S.jsx(et,{sx:{textAlign:"center"},children:S.jsx(U5,{component:"button",type:"button",onClick:n,sx:{color:"primary.main",textDecoration:"none","&:hover":{textDecoration:"underline"}},children:"Forgot your password?"})})]})]}),S.jsx(et,{sx:{mt:3,textAlign:"center"},children:S.jsx(jo,{severity:"info",message:"Demo: Use <EMAIL> / admin123 to login"})})]})})},Y3=({onBack:n,onEmailReset:r,onAdminReset:o})=>S.jsx(et,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)",display:"flex",alignItems:"center",justifyContent:"center",p:2},children:S.jsxs(Qo,{maxWidth:"md",children:[S.jsx(Fs,{}),S.jsxs(Qs,{title:"Forgot Password",subtitle:"Choose how you'd like to reset your password",maxWidth:600,children:[S.jsxs(A3,{direction:{xs:"column",md:"row"},spacing:3,sx:{mb:4},children:[S.jsx(et,{sx:{flex:1},children:S.jsx(ga,{elevation:2,sx:{p:3,cursor:"pointer",transition:"all 0.2s",borderRadius:2,border:"2px solid transparent","&:hover":{elevation:4,bgcolor:"#e3f2fd",transform:"translateY(-2px)",borderColor:"primary.main"}},onClick:r,children:S.jsxs(et,{sx:{display:"flex",alignItems:"center",gap:2},children:[S.jsx(Ya,{sx:{bgcolor:"#e3f2fd",color:"primary.main",width:56,height:56},children:S.jsx(rv,{sx:{fontSize:28}})}),S.jsxs(et,{children:[S.jsx(st,{variant:"h6",sx:{mb:.5},children:"Reset via Email OTP"}),S.jsx(st,{variant:"body2",color:"text.secondary",children:"We'll send a reset link to your email address"})]})]})})}),S.jsx(et,{sx:{flex:1},children:S.jsx(ga,{elevation:2,sx:{p:3,cursor:"pointer",transition:"all 0.2s",borderRadius:2,border:"2px solid transparent","&:hover":{elevation:4,bgcolor:"#fff3e0",transform:"translateY(-2px)",borderColor:"warning.main"}},onClick:o,children:S.jsxs(et,{sx:{display:"flex",alignItems:"center",gap:2},children:[S.jsx(Ya,{sx:{bgcolor:"#fff3e0",color:"warning.main",width:56,height:56},children:S.jsx(q3,{sx:{fontSize:28}})}),S.jsxs(et,{children:[S.jsx(st,{variant:"h6",sx:{mb:.5},children:"Request Admin Reset"}),S.jsx(st,{variant:"body2",color:"text.secondary",children:"Submit a request to your system administrator"})]})]})})})]}),S.jsx(Sr,{variant:"outlined",fullWidth:!0,size:"large",startIcon:S.jsx(wd,{}),onClick:n,sx:{borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",bgcolor:"#e3f2fd"}},children:"Back to Login"})]})]})}),I3=({onBack:n})=>{const[r,o]=C.useState(""),[i,u]=C.useState(!1),[f,d]=C.useState(!1),[p,g]=C.useState(null),h=async x=>{x.preventDefault(),u(!0),g(null);try{await zd.requestPasswordReset({email:r}),d(!0)}catch(R){g(R instanceof Error?R.message:"Failed to send reset email")}finally{u(!1)}},v=()=>{d(!1),o("")};return S.jsx(et,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)",display:"flex",alignItems:"center",justifyContent:"center",p:2},children:S.jsxs(Qo,{maxWidth:"sm",children:[S.jsx(Fs,{}),S.jsxs(Qs,{title:"Reset Password",subtitle:"Enter your email address to receive a password reset link",children:[p&&S.jsx(jo,{severity:"error",message:p,onClose:()=>g(null)}),f?S.jsxs(et,{sx:{textAlign:"center"},children:[S.jsxs(ga,{elevation:1,sx:{p:4,mb:3,bgcolor:"#e8f5e8",border:"1px solid",borderColor:"#a5d6a7",borderRadius:2},children:[S.jsx(Ya,{sx:{width:64,height:64,bgcolor:"success.main",mx:"auto",mb:2},children:S.jsx(av,{sx:{fontSize:32}})}),S.jsx(st,{variant:"h6",sx:{mb:1},children:"Check Your Email"}),S.jsxs(st,{variant:"body2",color:"text.secondary",children:["We've sent password reset instructions to",S.jsx("br",{}),S.jsx("strong",{children:r})]})]}),S.jsx(Sr,{variant:"contained",fullWidth:!0,size:"large",onClick:v,sx:{mb:2,background:"linear-gradient(135deg, #29b6f6 0%, #0277bd 100%)","&:hover":{background:"linear-gradient(135deg, #0277bd 0%, #01579b 100%)"}},children:"Resend Email"})]}):S.jsxs(et,{component:"form",onSubmit:h,children:[S.jsx(yl,{label:"Email Address",type:"email",value:r,onChange:x=>o(x.target.value),required:!0,placeholder:"Enter your registered email",autoComplete:"email"}),S.jsx(Sr,{type:"submit",variant:"contained",fullWidth:!0,size:"large",loading:i,loadingText:"Sending Reset Link...",startIcon:i?void 0:S.jsx(rv,{}),sx:{mb:3,background:"linear-gradient(135deg, #29b6f6 0%, #0277bd 100%)","&:hover":{background:"linear-gradient(135deg, #0277bd 0%, #01579b 100%)"}},children:"Send Reset Link"})]}),S.jsx(Sr,{variant:"outlined",fullWidth:!0,size:"large",startIcon:S.jsx(wd,{}),onClick:n,sx:{borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",bgcolor:"#e3f2fd"}},children:"Back to Options"})]})]})})},X3=({onBack:n})=>{const[r,o]=C.useState({email:"",employeeId:"",reason:""}),[i,u]=C.useState(!1),[f,d]=C.useState(!1),[p,g]=C.useState(""),[h,v]=C.useState(null),x=A=>T=>{o(E=>({...E,[A]:T.target.value})),v(null)},R=async A=>{A.preventDefault(),u(!0),v(null);try{const T=await zd.requestAdminReset(r);g(T.data?.requestId||""),d(!0)}catch(T){v(T instanceof Error?T.message:"Failed to submit request")}finally{u(!1)}};return S.jsx(et,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)",display:"flex",alignItems:"center",justifyContent:"center",p:2},children:S.jsxs(Qo,{maxWidth:"sm",children:[S.jsx(Fs,{}),S.jsxs(Qs,{title:"Request Admin Reset",subtitle:"Submit a request to your system administrator for password reset",children:[h&&S.jsx(jo,{severity:"error",message:h,onClose:()=>v(null)}),f?S.jsx(et,{sx:{textAlign:"center"},children:S.jsxs(ga,{elevation:1,sx:{p:4,mb:3,bgcolor:"#fff3e0",border:"1px solid",borderColor:"#ffcc02",borderRadius:2},children:[S.jsx(Ya,{sx:{width:64,height:64,bgcolor:"warning.main",mx:"auto",mb:2},children:S.jsx(av,{sx:{fontSize:32}})}),S.jsx(st,{variant:"h6",sx:{mb:2},children:"Request Submitted"}),S.jsxs(st,{variant:"body2",color:"text.secondary",sx:{mb:3},children:["Your password reset request has been sent to the administrator.",S.jsx("br",{}),"You will receive an email notification once processed."]}),p&&S.jsx(X0,{label:`Request ID: ${p}`,variant:"outlined",color:"warning",sx:{fontFamily:"monospace",fontSize:"0.875rem"}})]})}):S.jsxs(et,{component:"form",onSubmit:R,children:[S.jsx(yl,{label:"Email Address",type:"email",value:r.email,onChange:x("email"),required:!0,placeholder:"Enter your registered email",autoComplete:"email"}),S.jsx(yl,{label:"Employee ID",type:"text",value:r.employeeId,onChange:x("employeeId"),required:!0,placeholder:"Enter your employee ID",autoComplete:"username"}),S.jsx(yl,{label:"Reason for Reset",multiline:!0,rows:4,value:r.reason,onChange:x("reason"),required:!0,placeholder:"Please explain why you need a password reset...",sx:{mb:3}}),S.jsx(Sr,{type:"submit",variant:"contained",fullWidth:!0,size:"large",loading:i,loadingText:"Submitting Request...",startIcon:i?void 0:S.jsx(U3,{}),sx:{mb:3,background:"linear-gradient(135deg, #ff9800 0%, #f57c00 100%)","&:hover":{background:"linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)"}},children:"Submit Request"})]}),S.jsx(Sr,{variant:"outlined",fullWidth:!0,size:"large",startIcon:S.jsx(wd,{}),onClick:n,sx:{borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",bgcolor:"#e3f2fd"}},children:"Back to Options"})]})]})})},K3=({onLogout:n})=>{const r=[{label:"Total Employees",value:"156",icon:Vy,color:"primary"},{label:"Active Projects",value:"23",icon:Hy,color:"success"},{label:"Departments",value:"8",icon:Py,color:"warning"},{label:"This Month",value:"94%",icon:qy,color:"info"}],o=[{title:"Employee Management",description:"Manage employee records, profiles, and information",icon:Vy,color:"primary"},{title:"Human Resources",description:"Handle HR processes, leave management, and policies",icon:Py,color:"success"},{title:"Project Management",description:"Track projects, tasks, and team collaboration",icon:Hy,color:"warning"},{title:"Attendance System",description:"Monitor attendance, working hours, and schedules",icon:_3,color:"info"},{title:"Reports & Analytics",description:"Generate reports and view organizational analytics",icon:qy,color:"error"},{title:"System Settings",description:"Configure system preferences and user settings",icon:H3,color:"secondary"}],i=[{action:"New employee John Doe added to Engineering department",time:"2 hours ago"},{action:'Project "Website Redesign" marked as completed',time:"4 hours ago"},{action:"Monthly attendance report generated",time:"1 day ago"},{action:"System backup completed successfully",time:"2 days ago"}];return S.jsxs(et,{sx:{flexGrow:1,bgcolor:"#f5f5f5",minHeight:"100vh"},children:[S.jsx(bT,{position:"static",elevation:0,sx:{bgcolor:"primary.main"},children:S.jsxs(z3,{children:[S.jsx(st,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Organization Management System"}),S.jsxs(et,{sx:{display:"flex",alignItems:"center",gap:2},children:[S.jsx(L3,{sx:{cursor:"pointer"}}),S.jsx(Ya,{sx:{width:32,height:32,bgcolor:"primary.dark"},children:"A"}),S.jsx($3,{sx:{cursor:"pointer"},onClick:n})]})]})}),S.jsxs(Qo,{maxWidth:"xl",sx:{py:4},children:[S.jsxs(et,{sx:{mb:4},children:[S.jsx(st,{variant:"h4",sx:{mb:1,color:"text.primary"},children:"Welcome back, Admin!"}),S.jsx(st,{variant:"body1",color:"text.secondary",children:"Here's what's happening in your organization today."})]}),S.jsx(st,{variant:"h5",sx:{mb:3,color:"text.primary"},children:"Quick Stats"}),S.jsx(et,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"1fr 1fr",md:"1fr 1fr 1fr 1fr"},gap:3,mb:4},children:r.map((u,f)=>{const d=u.icon;return S.jsx(et,{children:S.jsx(Xf,{elevation:3,sx:{height:"100%",borderRadius:2},children:S.jsxs(Kf,{sx:{textAlign:"center",p:3},children:[S.jsx(Ya,{sx:{width:56,height:56,mx:"auto",mb:2,bgcolor:`${u.color}.main`},children:S.jsx(d,{sx:{fontSize:28}})}),S.jsx(st,{variant:"h4",sx:{mb:1,color:`${u.color}.main`},children:u.value}),S.jsx(st,{variant:"body2",color:"text.secondary",children:u.label})]})})},f)})}),S.jsx(st,{variant:"h5",sx:{mb:3,color:"text.primary"},children:"System Modules"}),S.jsx(et,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"1fr 1fr",md:"1fr 1fr 1fr"},gap:3,mb:4},children:o.map((u,f)=>{const d=u.icon;return S.jsx(et,{children:S.jsx(Xf,{elevation:3,sx:{height:"100%",borderRadius:2,cursor:"pointer",transition:"all 0.3s ease","&:hover":{elevation:8,transform:"translateY(-4px)"}},children:S.jsxs(Kf,{sx:{p:3},children:[S.jsxs(et,{sx:{display:"flex",alignItems:"center",mb:2},children:[S.jsx(Ya,{sx:{width:48,height:48,mr:2,bgcolor:`${u.color}.main`},children:S.jsx(d,{})}),S.jsx(st,{variant:"h6",sx:{color:"text.primary"},children:u.title})]}),S.jsx(st,{variant:"body2",color:"text.secondary",children:u.description})]})})},f)})}),S.jsxs(ga,{elevation:3,sx:{p:3,borderRadius:2},children:[S.jsx(st,{variant:"h6",sx:{mb:3},children:"Recent Activity"}),S.jsx(et,{sx:{display:"flex",flexDirection:"column",gap:2},children:i.map((u,f)=>S.jsxs(et,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",p:2,bgcolor:"grey.50",borderRadius:1},children:[S.jsx(st,{variant:"body2",sx:{flex:1},children:u.action}),S.jsx(X0,{label:u.time,size:"small",variant:"outlined",sx:{ml:2}})]},f))})]})]})]})},Q3=Ps({palette:{primary:{main:"#29b6f6",dark:"#0277bd",light:"#73e8ff"},secondary:{main:"#ff9800",dark:"#f57c00",light:"#ffcc02"},background:{default:"#f5f5f5",paper:"#ffffff"},success:{main:"#4caf50"},warning:{main:"#ff9800"},info:{main:"#29b6f6"},error:{main:"#f44336"}},typography:{fontFamily:'"Inter", "Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},shape:{borderRadius:12},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",fontWeight:500}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 4px 20px rgba(0,0,0,0.1)"}}}}});function F3(){const[n,r]=C.useState("login"),o=u=>{r(u)},i=()=>{switch(n){case"login":return S.jsx(G3,{onForgotPassword:()=>o("forgotPassword"),onLoginSuccess:()=>o("dashboard")});case"forgotPassword":return S.jsx(Y3,{onBack:()=>o("login"),onEmailReset:()=>o("emailReset"),onAdminReset:()=>o("adminReset")});case"emailReset":return S.jsx(I3,{onBack:()=>o("forgotPassword")});case"adminReset":return S.jsx(X3,{onBack:()=>o("forgotPassword")});case"dashboard":return S.jsx(K3,{onLogout:()=>o("login")});default:return null}};return S.jsxs(iC,{theme:Q3,children:[S.jsx(BE,{}),S.jsx("div",{style:{minHeight:"100vh"},children:i()})]})}A1.createRoot(document.getElementById("root")).render(S.jsx(C.StrictMode,{children:S.jsx(F3,{})}));
