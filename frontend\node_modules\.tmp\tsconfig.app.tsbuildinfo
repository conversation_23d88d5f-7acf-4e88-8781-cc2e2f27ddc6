{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/common/button.tsx", "../../src/components/common/dialog.tsx", "../../src/components/common/formcard.tsx", "../../src/components/common/inputfield.tsx", "../../src/components/common/loader.tsx", "../../src/components/data-display/avatar.tsx", "../../src/components/data-display/card.tsx", "../../src/components/data-display/chip.tsx", "../../src/components/data-display/table.tsx", "../../src/components/feedback/alert.tsx", "../../src/components/feedback/alertmessage.tsx", "../../src/components/feedback/snackbar.tsx", "../../src/components/feedback/toast.tsx", "../../src/components/layout/companylogo.tsx", "../../src/components/layout/footer.tsx", "../../src/components/layout/header.tsx", "../../src/components/layout/navbar.tsx", "../../src/components/layout/sidebar.tsx", "../../src/hooks/useapi.ts", "../../src/hooks/useauth.tsx", "../../src/hooks/usedebounce.ts", "../../src/hooks/uselocalstorage.ts", "../../src/modules/auth/components/loginform.tsx", "../../src/modules/auth/pages/adminreset.tsx", "../../src/modules/auth/pages/emailreset.tsx", "../../src/modules/auth/pages/forgotpassword.tsx", "../../src/modules/auth/pages/login.tsx", "../../src/modules/auth/services/authservice.ts", "../../src/modules/dashboard/pages/dashboard.tsx", "../../src/modules/ems/profilepage.tsx", "../../src/modules/hrms/employeespage.tsx", "../../src/modules/pms/projectspage.tsx", "../../src/modules/settings/settingspage.tsx", "../../src/routes/authrouter.tsx", "../../src/services/apiclient.ts", "../../src/services/endpoints.ts", "../../src/store/index.ts", "../../src/theme/index.ts", "../../src/types/auth.ts", "../../src/utils/constants.ts", "../../src/utils/dateutils.ts", "../../src/utils/formatters.ts", "../../src/utils/validation.ts"], "version": "5.8.3"}