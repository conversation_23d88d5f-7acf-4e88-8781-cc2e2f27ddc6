export const APP_CONFIG = {
  name: 'Organization Management System',
  version: '1.0.0',
  description: 'Complete organization management solution',
};

export const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  retryAttempts: 3,
};

export const STORAGE_KEYS = {
  authToken: 'auth_token',
  userPreferences: 'user_preferences',
  theme: 'app_theme',
  language: 'app_language',
};

export const ROUTES = {
  home: '/',
  login: '/login',
  register: '/register',
  dashboard: '/dashboard',
  profile: '/profile',
  settings: '/settings',
  employees: '/employees',
  projects: '/projects',
  tasks: '/tasks',
  attendance: '/attendance',
  reports: '/reports',
};

export const USER_ROLES = {
  admin: 'admin',
  manager: 'manager',
  employee: 'employee',
  hr: 'hr',
} as const;

export const PROJECT_STATUS = {
  active: 'active',
  completed: 'completed',
  onHold: 'on-hold',
  cancelled: 'cancelled',
} as const;

export const TASK_STATUS = {
  todo: 'todo',
  inProgress: 'in-progress',
  review: 'review',
  done: 'done',
} as const;

export const TASK_PRIORITY = {
  low: 'low',
  medium: 'medium',
  high: 'high',
  urgent: 'urgent',
} as const;

export const LEAVE_STATUS = {
  pending: 'pending',
  approved: 'approved',
  rejected: 'rejected',
  cancelled: 'cancelled',
} as const;

export const LEAVE_TYPES = {
  annual: 'annual',
  sick: 'sick',
  personal: 'personal',
  maternity: 'maternity',
  paternity: 'paternity',
  emergency: 'emergency',
} as const;

export const DEPARTMENTS = [
  'Engineering',
  'Marketing',
  'Sales',
  'Human Resources',
  'Finance',
  'Operations',
  'Customer Support',
  'Design',
] as const;

export const NOTIFICATION_TYPES = {
  info: 'info',
  success: 'success',
  warning: 'warning',
  error: 'error',
} as const;

export const DATE_FORMATS = {
  short: 'MM/dd/yyyy',
  long: 'MMMM dd, yyyy',
  time: 'HH:mm',
  dateTime: 'MM/dd/yyyy HH:mm',
} as const;
