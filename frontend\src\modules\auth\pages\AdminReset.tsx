import React, { useState } from 'react';
import { Box, Container, Paper, Avatar, Typography, Chip } from '@mui/material';
import { ArrowBack, Send, CheckCircle } from '@mui/icons-material';

import { InputField } from '../../../components/common/InputField';
import { Button } from '../../../components/common/Button';
import { FormCard } from '../../../components/common/FormCard';
import { AlertMessage } from '../../../components/feedback/AlertMessage';
import { CompanyLogo } from '../../../components/layout/CompanyLogo';
import { authService } from '../services/authService';
import { AdminResetRequest } from '../../../types/auth.ts';

interface AdminResetProps {
  onBack: () => void;
}

export const AdminReset: React.FC<AdminResetProps> = ({ onBack }) => {
  const [formData, setFormData] = useState<AdminResetRequest>({
    email: '',
    employeeId: '',
    reason: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [requestId, setRequestId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof AdminResetRequest) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: event.target.value }));
    setError(null);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.requestAdminReset(formData);
      setRequestId(response.data?.requestId || '');
      setIsSubmitted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit request');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Container maxWidth="sm">
        <CompanyLogo />

        <FormCard
          title="Request Admin Reset"
          subtitle="Submit a request to your system administrator for password reset"
        >
          {error && (
            <AlertMessage
              severity="error"
              message={error}
              onClose={() => setError(null)}
            />
          )}

          {!isSubmitted ? (
            <Box component="form" onSubmit={handleSubmit}>
              <InputField
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={handleInputChange('email')}
                required
                placeholder="Enter your registered email"
                autoComplete="email"
              />

              <InputField
                label="Employee ID"
                type="text"
                value={formData.employeeId}
                onChange={handleInputChange('employeeId')}
                required
                placeholder="Enter your employee ID"
                autoComplete="username"
              />

              <InputField
                label="Reason for Reset"
                multiline
                rows={4}
                value={formData.reason}
                onChange={handleInputChange('reason')}
                required
                placeholder="Please explain why you need a password reset..."
                sx={{ mb: 3 }}
              />

              <Button
                type="submit"
                variant="contained"
                fullWidth
                size="large"
                loading={isLoading}
                loadingText="Submitting Request..."
                startIcon={!isLoading ? <Send /> : undefined}
                sx={{
                  mb: 3,
                  background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)',
                  },
                }}
              >
                Submit Request
              </Button>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Paper
                elevation={1}
                sx={{
                  p: 4,
                  mb: 3,
                  bgcolor: '#fff3e0',
                  border: '1px solid',
                  borderColor: '#ffcc02',
                  borderRadius: 2,
                }}
              >
                <Avatar
                  sx={{
                    width: 64,
                    height: 64,
                    bgcolor: 'warning.main',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <CheckCircle sx={{ fontSize: 32 }} />
                </Avatar>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Request Submitted
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Your password reset request has been sent to the administrator.
                  <br />
                  You will receive an email notification once processed.
                </Typography>

                {requestId && (
                  <Chip
                    label={`Request ID: ${requestId}`}
                    variant="outlined"
                    color="warning"
                    sx={{ 
                      fontFamily: 'monospace',
                      fontSize: '0.875rem',
                    }}
                  />
                )}
              </Paper>
            </Box>
          )}

          <Button
            variant="outlined"
            fullWidth
            size="large"
            startIcon={<ArrowBack />}
            onClick={onBack}
            sx={{
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                borderColor: 'primary.dark',
                bgcolor: '#e3f2fd',
              },
            }}
          >
            Back to Options
          </Button>
        </FormCard>
      </Container>
    </Box>
  );
};