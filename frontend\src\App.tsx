import { useState } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { Login } from './modules/auth/pages/Login';
import { ForgotPassword } from './modules/auth/pages/ForgotPassword';
import { EmailReset } from './modules/auth/pages/EmailReset';
import { AdminReset } from './modules/auth/pages/AdminReset';
import { Dashboard } from './modules/dashboard/pages/Dashboard';

type AuthScreen = 'login' | 'forgotPassword' | 'emailReset' | 'adminReset' | 'dashboard';

// Create Material-UI theme with sky blue primary color
const theme = createTheme({
  palette: {
    primary: {
      main: '#29b6f6', // Sky blue
      dark: '#0277bd',
      light: '#73e8ff',
    },
    secondary: {
      main: '#ff9800', // Orange for admin-related features
      dark: '#f57c00',
      light: '#ffcc02',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    success: {
      main: '#4caf50',
    },
    warning: {
      main: '#ff9800',
    },
    info: {
      main: '#29b6f6',
    },
    error: {
      main: '#f44336',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<AuthScreen>('login');

  const handleNavigation = (screen: AuthScreen) => {
    setCurrentScreen(screen);
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'login':
        return (
          <Login
            onForgotPassword={() => handleNavigation('forgotPassword')}
            onLoginSuccess={() => handleNavigation('dashboard')}
          />
        );
      case 'forgotPassword':
        return (
          <ForgotPassword
            onBack={() => handleNavigation('login')}
            onEmailReset={() => handleNavigation('emailReset')}
            onAdminReset={() => handleNavigation('adminReset')}
          />
        );
      case 'emailReset':
        return (
          <EmailReset
            onBack={() => handleNavigation('forgotPassword')}
          />
        );
      case 'adminReset':
        return (
          <AdminReset
            onBack={() => handleNavigation('forgotPassword')}
          />
        );
      case 'dashboard':
        return (
          <Dashboard
            onLogout={() => handleNavigation('login')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div style={{ minHeight: '100vh' }}>
        {renderScreen()}
      </div>
    </ThemeProvider>
  );
}