import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Container,
  Avatar,
  AppBar,
  Toolbar,
  Paper,
  Chip,
} from '@mui/material';
import {
  Business,
  People,
  Assignment,
  CalendarToday,
  BarChart,
  Settings,
  Notifications,
  ExitToApp,
} from '@mui/icons-material';

interface DashboardProps {
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const stats = [
    { label: 'Total Employees', value: '156', icon: People, color: 'primary' },
    { label: 'Active Projects', value: '23', icon: Assignment, color: 'success' },
    { label: 'Departments', value: '8', icon: Business, color: 'warning' },
    { label: 'This Month', value: '94%', icon: BarChart, color: 'info' },
  ];

  const modules = [
    {
      title: 'Employee Management',
      description: 'Manage employee records, profiles, and information',
      icon: People,
      color: 'primary',
    },
    {
      title: 'Human Resources',
      description: 'Handle HR processes, leave management, and policies',
      icon: Business,
      color: 'success',
    },
    {
      title: 'Project Management',
      description: 'Track projects, tasks, and team collaboration',
      icon: Assignment,
      color: 'warning',
    },
    {
      title: 'Attendance System',
      description: 'Monitor attendance, working hours, and schedules',
      icon: CalendarToday,
      color: 'info',
    },
    {
      title: 'Reports & Analytics',
      description: 'Generate reports and view organizational analytics',
      icon: BarChart,
      color: 'error',
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences and user settings',
      icon: Settings,
      color: 'secondary',
    },
  ];

  const recentActivities = [
    { action: 'New employee John Doe added to Engineering department', time: '2 hours ago' },
    { action: 'Project "Website Redesign" marked as completed', time: '4 hours ago' },
    { action: 'Monthly attendance report generated', time: '1 day ago' },
    { action: 'System backup completed successfully', time: '2 days ago' },
  ];

  return (
    <Box sx={{ flexGrow: 1, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <AppBar position="static" elevation={0} sx={{ bgcolor: 'primary.main' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Organization Management System
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Notifications sx={{ cursor: 'pointer' }} />
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.dark' }}>
              A
            </Avatar>
            <ExitToApp sx={{ cursor: 'pointer' }} onClick={onLogout} />
          </Box>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Welcome Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ mb: 1, color: 'text.primary' }}>
            Welcome back, Admin!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's what's happening in your organization today.
          </Typography>
        </Box>

        {/* Quick Stats */}
        <Typography variant="h5" sx={{ mb: 3, color: 'text.primary' }}>
          Quick Stats
        </Typography>

        <Box 
          sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
            gap: 3,
            mb: 4 
          }}
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Box key={index}>
                <Card elevation={3} sx={{ height: '100%', borderRadius: 2 }}>
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        mx: 'auto',
                        mb: 2,
                        bgcolor: `${stat.color}.main`,
                      }}
                    >
                      <Icon sx={{ fontSize: 28 }} />
                    </Avatar>
                    <Typography variant="h4" sx={{ mb: 1, color: `${stat.color}.main` }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
            );
          })}
        </Box>

        {/* System Modules */}
        <Typography variant="h5" sx={{ mb: 3, color: 'text.primary' }}>
          System Modules
        </Typography>

        <Box 
          sx={{ 
            display: 'grid', 
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' },
            gap: 3,
            mb: 4 
          }}
        >
          {modules.map((module, index) => {
            const Icon = module.icon;
            return (
              <Box key={index}>
                <Card
                  elevation={3}
                  sx={{
                    height: '100%',
                    borderRadius: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      elevation: 8,
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          mr: 2,
                          bgcolor: `${module.color}.main`,
                        }}
                      >
                        <Icon />
                      </Avatar>
                      <Typography variant="h6" sx={{ color: 'text.primary' }}>
                        {module.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {module.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
            );
          })}
        </Box>

        {/* Recent Activity */}
        <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Recent Activity
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {recentActivities.map((activity, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                }}
              >
                <Typography variant="body2" sx={{ flex: 1 }}>
                  {activity.action}
                </Typography>
                <Chip
                  label={activity.time}
                  size="small"
                  variant="outlined"
                  sx={{ ml: 2 }}
                />
              </Box>
            ))}
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};
